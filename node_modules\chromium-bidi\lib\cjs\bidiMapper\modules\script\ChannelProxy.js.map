{"version": 3, "file": "ChannelProxy.js", "sourceRoot": "", "sources": ["../../../../../src/bidiMapper/modules/script/ChannelProxy.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;;;GAgBG;;;AAIH,+DAAmE;AACnE,kDAA6D;AAC7D,oDAA8C;AAK9C;;GAEG;AACH,MAAa,YAAY;IACd,WAAW,CAA2B;IAEtC,GAAG,GAAG,IAAA,gBAAM,GAAE,CAAC;IACf,OAAO,CAAY;IAE5B,YAAY,OAAiC,EAAE,MAAiB;QAC9D,IAAI,CAAC,WAAW,GAAG,OAAO,CAAC;QAC3B,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC;IACxB,CAAC;IAED;;;OAGG;IACH,KAAK,CAAC,IAAI,CAAC,KAAY,EAAE,YAA0B;QACjD,MAAM,aAAa,GAAG,MAAM,YAAY,CAAC,0BAA0B,CAAC,KAAK,CAAC,CAAC;QAC3E,MAAM,iBAAiB,GAAG,MAAM,YAAY,CAAC,wBAAwB,CACnE,KAAK,EACL,aAAa,CACd,CAAC;QAEF,KAAK,IAAI,CAAC,cAAc,CAAC,KAAK,EAAE,aAAa,EAAE,YAAY,CAAC,CAAC;QAC7D,OAAO,iBAAiB,CAAC;IAC3B,CAAC;IAED,8DAA8D;IAC9D,KAAK,CAAC,uBAAuB,CAAC,KAAY,EAAE,YAA0B;QACpE,IAAI,CAAC;YACH,MAAM,aAAa,GAAG,MAAM,IAAI,CAAC,oBAAoB,CAAC,KAAK,CAAC,CAAC;YAC7D,KAAK,IAAI,CAAC,cAAc,CAAC,KAAK,EAAE,aAAa,EAAE,YAAY,CAAC,CAAC;QAC/D,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,OAAO,EAAE,CAAC,gBAAO,CAAC,UAAU,EAAE,KAAK,CAAC,CAAC;QAC5C,CAAC;IACH,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,0BAA0B;QAC/B,MAAM,WAAW,GAAG,MAAM,CAAC,GAAG,EAAE;YAC9B,MAAM,KAAK,GAAc,EAAE,CAAC;YAC5B,IAAI,qBAAqB,GAAwB,IAAI,CAAC;YAEtD,OAAO;gBACL;;;mBAGG;gBACH,KAAK,CAAC,UAAU;oBACd,MAAM,SAAS,GACb,KAAK,CAAC,MAAM,GAAG,CAAC;wBACd,CAAC,CAAC,OAAO,CAAC,OAAO,EAAE;wBACnB,CAAC,CAAC,IAAI,OAAO,CAAO,CAAC,OAAO,EAAE,EAAE;4BAC5B,qBAAqB,GAAG,OAAO,CAAC;wBAClC,CAAC,CAAC,CAAC;oBACT,MAAM,SAAS,CAAC;oBAChB,OAAO,KAAK,CAAC,KAAK,EAAE,CAAC;gBACvB,CAAC;gBAED;;;mBAGG;gBACH,WAAW,CAAC,OAAgB;oBAC1B,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;oBACpB,IAAI,qBAAqB,KAAK,IAAI,EAAE,CAAC;wBACnC,qBAAqB,EAAE,CAAC;wBACxB,qBAAqB,GAAG,IAAI,CAAC;oBAC/B,CAAC;gBACH,CAAC;aACF,CAAC;QACJ,CAAC,CAAC,CAAC;QAEH,OAAO,IAAI,WAAW,KAAK,CAAC;IAC9B,CAAC;IAED,iDAAiD;IACjD,MAAM,CAAC,KAAK,CAAC,0BAA0B,CACrC,KAAY;QAEZ,MAAM,yBAAyB,GAAG,MAAM,KAAK,CAAC,SAAS,CAAC,WAAW,CACjE,kBAAkB,EAClB;YACE,UAAU,EAAE,IAAI,CAAC,0BAA0B,EAAE;YAC7C,SAAS,EAAE,KAAK,CAAC,kBAAkB;YACnC,oBAAoB,EAAE;gBACpB,aAAa,0EAC8C;aAC5D;SACF,CACF,CAAC;QACF,IACE,yBAAyB,CAAC,gBAAgB;YAC1C,yBAAyB,CAAC,MAAM,CAAC,QAAQ,KAAK,SAAS,EACvD,CAAC;YACD,MAAM,IAAI,KAAK,CAAC,uBAAuB,CAAC,CAAC;QAC3C,CAAC;QACD,OAAO,yBAAyB,CAAC,MAAM,CAAC,QAAQ,CAAC;IACnD,CAAC;IAED,4EAA4E;IAC5E,MAAM,CAAC,KAAK,CAAC,wBAAwB,CACnC,KAAY,EACZ,aAA4B;QAE5B,MAAM,oBAAoB,GAAG,MAAM,KAAK,CAAC,SAAS,CAAC,WAAW,CAC5D,wBAAwB,EACxB;YACE,mBAAmB,EAAE,MAAM,CACzB,CAAC,aAAuD,EAAE,EAAE;gBAC1D,OAAO,aAAa,CAAC,WAAW,CAAC;YACnC,CAAC,CACF;YACD,SAAS,EAAE,CAAC,EAAC,QAAQ,EAAE,aAAa,EAAC,CAAC;YACtC,kBAAkB,EAAE,KAAK,CAAC,kBAAkB;YAC5C,oBAAoB,EAAE;gBACpB,aAAa,0EAC8C;aAC5D;SACF,CACF,CAAC;QACF,oCAAoC;QACpC,OAAO,oBAAoB,CAAC,MAAM,CAAC,QAAS,CAAC;IAC/C,CAAC;IAED,4EAA4E;IAC5E,KAAK,CAAC,cAAc,CAClB,KAAY,EACZ,aAA4B,EAC5B,YAA0B;QAE1B,8BAA8B;QAC9B,SAAS,CAAC;YACR,IAAI,CAAC;gBACH,MAAM,OAAO,GAAG,MAAM,KAAK,CAAC,SAAS,CAAC,WAAW,CAC/C,wBAAwB,EACxB;oBACE,mBAAmB,EAAE,MAAM,CACzB,KAAK,EAAE,aAAmD,EAAE,EAAE,CAC5D,MAAM,aAAa,CAAC,UAAU,EAAE,CACnC;oBACD,SAAS,EAAE;wBACT;4BACE,QAAQ,EAAE,aAAa;yBACxB;qBACF;oBACD,YAAY,EAAE,IAAI;oBAClB,kBAAkB,EAAE,KAAK,CAAC,kBAAkB;oBAC5C,oBAAoB,EAAE;wBACpB,aAAa,sEAC4C;wBACzD,QAAQ,EACN,IAAI,CAAC,WAAW,CAAC,oBAAoB,EAAE,cAAc;4BACrD,SAAS;qBACZ;iBACF,CACF,CAAC;gBAEF,IAAI,OAAO,CAAC,gBAAgB,EAAE,CAAC;oBAC7B,MAAM,IAAI,KAAK,CAAC,wCAAwC,EAAE;wBACxD,KAAK,EAAE,OAAO,CAAC,gBAAgB;qBAChC,CAAC,CAAC;gBACL,CAAC;gBAED,KAAK,MAAM,eAAe,IAAI,KAAK,CAAC,0BAA0B,EAAE,CAAC;oBAC/D,YAAY,CAAC,aAAa,CACxB;wBACE,IAAI,EAAE,OAAO;wBACb,MAAM,EAAE,0BAAY,CAAC,MAAM,CAAC,UAAU,CAAC,OAAO;wBAC9C,MAAM,EAAE;4BACN,OAAO,EAAE,IAAI,CAAC,WAAW,CAAC,OAAO;4BACjC,IAAI,EAAE,KAAK,CAAC,cAAc,CACxB,OAAO,EACP,IAAI,CAAC,WAAW,CAAC,SAAS,4CAA+B,CAC1D;4BACD,MAAM,EAAE,KAAK,CAAC,MAAM;yBACrB;qBACF,EACD,eAAe,CAAC,EAAE,CACnB,CAAC;gBACJ,CAAC;YACH,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,uEAAuE;gBACvE,iBAAiB;gBACjB,IAAI,CAAC,OAAO,EAAE,CAAC,gBAAO,CAAC,UAAU,EAAE,KAAK,CAAC,CAAC;gBAC1C,MAAM;YACR,CAAC;QACH,CAAC;IACH,CAAC;IAED;;;;;;;OAOG;IACH,KAAK,CAAC,oBAAoB,CAAC,KAAY;QACrC,MAAM,mBAAmB,GAAG,MAAM,KAAK,CAAC,SAAS,CAAC,WAAW,CAC3D,wBAAwB,EACxB;YACE,mBAAmB,EAAE,MAAM,CAAC,CAAC,EAAU,EAAE,EAAE;gBACzC,MAAM,CAAC,GAAG,MAET,CAAC;gBACF,IAAI,CAAC,CAAC,EAAE,CAAC,KAAK,SAAS,EAAE,CAAC;oBACxB,iEAAiE;oBACjE,sDAAsD;oBACtD,uDAAuD;oBACvD,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC,CAAC,CAAC;gBACrD,CAAC;gBACD,kEAAkE;gBAClE,0CAA0C;gBAC1C,MAAM,YAAY,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC;gBAC3B,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC;gBACb,OAAO,YAAY,CAAC;YACtB,CAAC,CAAC;YACF,SAAS,EAAE,CAAC,EAAC,KAAK,EAAE,IAAI,CAAC,GAAG,EAAC,CAAC;YAC9B,kBAAkB,EAAE,KAAK,CAAC,kBAAkB;YAC5C,YAAY,EAAE,IAAI;YAClB,oBAAoB,EAAE;gBACpB,aAAa,0EAC8C;aAC5D;SACF,CACF,CAAC;QACF,IACE,mBAAmB,CAAC,gBAAgB,KAAK,SAAS;YAClD,mBAAmB,CAAC,MAAM,CAAC,QAAQ,KAAK,SAAS,EACjD,CAAC;YACD,MAAM,IAAI,KAAK,CAAC,sCAAsC,IAAI,CAAC,GAAG,IAAI,CAAC,CAAC;QACtE,CAAC;QACD,OAAO,mBAAmB,CAAC,MAAM,CAAC,QAAQ,CAAC;IAC7C,CAAC;IAED;;;;;;;;;;OAUG;IACH,kBAAkB;QAChB,MAAM,QAAQ,GAAG,MAAM,CACrB,CAAC,EAAU,EAAE,YAAoC,EAAE,EAAE;YACnD,MAAM,CAAC,GAAG,MAET,CAAC;YACF,IAAI,CAAC,CAAC,EAAE,CAAC,KAAK,SAAS,EAAE,CAAC;gBACxB,kEAAkE;gBAClE,sBAAsB;gBACtB,CAAC,CAAC,EAAE,CAAC,GAAG,YAAY,CAAC;YACvB,CAAC;iBAAM,CAAC;gBACN,sEAAsE;gBACtE,4DAA4D;gBAC3D,CAAC,CAAC,EAAE,CAA0B,CAAC,YAAY,CAAC,CAAC;gBAC9C,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC;YACf,CAAC;YACD,OAAO,YAAY,CAAC,WAAW,CAAC;QAClC,CAAC,CACF,CAAC;QACF,MAAM,gBAAgB,GAAG,YAAY,CAAC,0BAA0B,EAAE,CAAC;QACnE,OAAO,IAAI,QAAQ,MAAM,IAAI,CAAC,GAAG,KAAK,gBAAgB,GAAG,CAAC;IAC5D,CAAC;CACF;AA9QD,oCA8QC"}