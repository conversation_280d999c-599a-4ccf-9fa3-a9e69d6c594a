#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据处理模块
负责数据清洗、转换、验证和格式化
"""

import re
import json
from datetime import datetime
from typing import List, Dict, Any, Optional
from PyQt5.QtCore import QAbstractTableModel, Qt, QVariant
from PyQt5.QtGui import QPixmap, QIcon
from utils.logger import get_logger


class ProductDataModel(QAbstractTableModel):
    """商品数据表格模型"""
    
    def __init__(self, data: List[Dict[str, Any]] = None):
        """
        初始化数据模型
        
        Args:
            data: 商品数据列表
        """
        super().__init__()
        self.logger = get_logger(self.__class__.__name__)
        self._data = data or []
        self._headers = ["序号", "标题", "价格", "想要", "浏览", "转化率", "状态", "链接"]
        
    def rowCount(self, parent=None):
        """返回行数"""
        return len(self._data)
    
    def columnCount(self, parent=None):
        """返回列数"""
        return len(self._headers)
    
    def data(self, index, role=Qt.DisplayRole):
        """返回单元格数据"""
        if not index.isValid() or index.row() >= len(self._data):
            return QVariant()
        
        item = self._data[index.row()]
        column = index.column()
        
        if role == Qt.DisplayRole:
            if column == 0:  # 序号
                return index.row() + 1
            elif column == 1:  # 标题
                title = item.get('title', '')
                return title[:50] + '...' if len(title) > 50 else title
            elif column == 2:  # 价格
                price = item.get('price', 0)
                return f"¥{price:.2f}" if price > 0 else "未知"
            elif column == 3:  # 想要
                return item.get('wantCnt', 0) or item.get('wantCount', 0)
            elif column == 4:  # 浏览
                return item.get('browseCnt', 0)
            elif column == 5:  # 转化率
                rate = item.get('conversionRate', 0)
                return f"{rate:.1f}%" if rate > 0 else "0%"
            elif column == 6:  # 状态
                return item.get('status', '在售')
            elif column == 7:  # 链接
                return "查看"
        
        elif role == Qt.ToolTipRole:
            if column == 1:  # 标题完整内容
                return item.get('title', '')
            elif column == 7:  # 链接完整URL
                return item.get('link', '')
        
        elif role == Qt.UserRole:
            # 返回完整的商品数据
            return item
        
        return QVariant()
    
    def headerData(self, section, orientation, role=Qt.DisplayRole):
        """返回表头数据"""
        if orientation == Qt.Horizontal and role == Qt.DisplayRole:
            return self._headers[section]
        return QVariant()
    
    def update_data(self, data: List[Dict[str, Any]]):
        """更新数据"""
        self.beginResetModel()
        self._data = data
        self.endResetModel()
        self.logger.info(f"数据模型已更新，共{len(data)}条记录")
    
    def get_item(self, row: int) -> Optional[Dict[str, Any]]:
        """获取指定行的商品数据"""
        if 0 <= row < len(self._data):
            return self._data[row]
        return None
    
    def get_all_data(self) -> List[Dict[str, Any]]:
        """获取所有数据"""
        return self._data.copy()


class DataProcessor:
    """数据处理器"""
    
    def __init__(self):
        """初始化数据处理器"""
        self.logger = get_logger(self.__class__.__name__)
    
    def clean_and_validate(self, raw_data: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        清洗和验证数据
        
        Args:
            raw_data: 原始数据列表
        
        Returns:
            List[Dict]: 清洗后的数据
        """
        cleaned_data = []
        
        for item in raw_data:
            try:
                cleaned_item = self._clean_single_item(item)
                if self._validate_item(cleaned_item):
                    cleaned_data.append(cleaned_item)
                else:
                    self.logger.warning(f"数据验证失败: {item.get('title', 'Unknown')}")
            except Exception as e:
                self.logger.error(f"数据清洗失败: {e}")
        
        self.logger.info(f"数据清洗完成，有效数据{len(cleaned_data)}条")
        return cleaned_data
    
    def _clean_single_item(self, item: Dict[str, Any]) -> Dict[str, Any]:
        """
        清洗单个商品数据
        
        Args:
            item: 原始商品数据
        
        Returns:
            Dict: 清洗后的商品数据
        """
        cleaned = {}
        
        # 基础字段清洗
        cleaned['itemId'] = str(item.get('itemId', '')).strip()
        cleaned['title'] = self._clean_text(item.get('title', ''))
        cleaned['price'] = self._clean_price(item.get('price', 0))
        cleaned['wantCount'] = self._clean_number(item.get('wantCnt', 0) or item.get('wantCount', 0))
        cleaned['browseCnt'] = self._clean_number(item.get('browseCnt', 0))
        cleaned['status'] = self._clean_status(item.get('status', '在售'))
        cleaned['link'] = self._clean_url(item.get('link', ''))
        cleaned['imageUrl'] = self._clean_url(item.get('imageUrl', ''))
        
        # 时间字段
        cleaned['timestamp'] = item.get('timestamp', datetime.now().strftime('%Y-%m-%d %H:%M:%S'))
        
        # 上架时间处理
        polish_time = item.get('proPolishTime')
        if polish_time:
            try:
                if isinstance(polish_time, str) and polish_time.isdigit():
                    polish_time = int(polish_time)
                if isinstance(polish_time, (int, float)):
                    cleaned['publishTime'] = datetime.fromtimestamp(polish_time / 1000).strftime('%Y-%m-%d %H:%M:%S')
                else:
                    cleaned['publishTime'] = str(polish_time)
            except:
                cleaned['publishTime'] = '未知'
        else:
            cleaned['publishTime'] = '未知'
        
        # 计算转化率
        want_count = cleaned['wantCount']
        browse_count = cleaned['browseCnt']
        if browse_count > 0:
            cleaned['conversionRate'] = round(want_count / browse_count * 100, 2)
        else:
            cleaned['conversionRate'] = 0
        
        # 详细信息
        cleaned['desc'] = self._clean_text(item.get('desc', ''))
        cleaned['favCnt'] = self._clean_number(item.get('favCnt', 0))
        
        # 图片信息
        image_infos = item.get('imageInfos', [])
        if isinstance(image_infos, list) and image_infos:
            cleaned['images'] = [img.get('url', '') for img in image_infos if isinstance(img, dict)]
        else:
            cleaned['images'] = [cleaned['imageUrl']] if cleaned['imageUrl'] else []
        
        return cleaned
    
    def _clean_text(self, text: Any) -> str:
        """清洗文本字段"""
        if not text:
            return ''
        
        text = str(text).strip()
        # 移除多余的空白字符
        text = re.sub(r'\s+', ' ', text)
        # 移除特殊字符
        text = re.sub(r'[^\w\s\u4e00-\u9fff\-.,!?()（）]', '', text)
        return text
    
    def _clean_price(self, price: Any) -> float:
        """清洗价格字段"""
        if isinstance(price, (int, float)):
            return max(0, float(price))
        
        if isinstance(price, str):
            # 提取数字
            price_match = re.search(r'(\d+(?:\.\d+)?)', price.replace(',', ''))
            if price_match:
                return float(price_match.group(1))
        
        return 0.0
    
    def _clean_number(self, number: Any) -> int:
        """清洗数字字段"""
        if isinstance(number, (int, float)):
            return max(0, int(number))
        
        if isinstance(number, str):
            # 提取数字
            number_match = re.search(r'(\d+)', number.replace(',', ''))
            if number_match:
                return int(number_match.group(1))
        
        return 0
    
    def _clean_status(self, status: Any) -> str:
        """清洗状态字段"""
        status = str(status).strip()
        if '售' in status or 'sold' in status.lower():
            return '已售出'
        return '在售'
    
    def _clean_url(self, url: Any) -> str:
        """清洗URL字段"""
        if not url:
            return ''
        
        url = str(url).strip()
        if not url.startswith(('http://', 'https://')):
            if url.startswith('//'):
                url = 'https:' + url
            elif url.startswith('/'):
                url = 'https://www.goofish.com' + url
        
        return url
    
    def _validate_item(self, item: Dict[str, Any]) -> bool:
        """
        验证商品数据
        
        Args:
            item: 商品数据
        
        Returns:
            bool: 是否有效
        """
        # 必须有标题和商品ID
        if not item.get('title') or not item.get('itemId'):
            return False
        
        # 价格必须是有效数字
        if not isinstance(item.get('price'), (int, float)) or item['price'] < 0:
            return False
        
        # 想要人数必须是非负整数
        if not isinstance(item.get('wantCount'), int) or item['wantCount'] < 0:
            return False
        
        return True
    
    def sort_data(self, data: List[Dict[str, Any]], sort_key: str, reverse: bool = False) -> List[Dict[str, Any]]:
        """
        排序数据
        
        Args:
            data: 数据列表
            sort_key: 排序字段
            reverse: 是否倒序
        
        Returns:
            List[Dict]: 排序后的数据
        """
        try:
            if sort_key in ['wantCount', 'browseCnt', 'price', 'conversionRate']:
                return sorted(data, key=lambda x: x.get(sort_key, 0), reverse=reverse)
            else:
                return sorted(data, key=lambda x: str(x.get(sort_key, '')), reverse=reverse)
        except Exception as e:
            self.logger.error(f"数据排序失败: {e}")
            return data
    
    def filter_data(self, data: List[Dict[str, Any]], filters: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        过滤数据
        
        Args:
            data: 数据列表
            filters: 过滤条件
        
        Returns:
            List[Dict]: 过滤后的数据
        """
        filtered_data = data
        
        try:
            # 最小想要人数过滤
            min_want = filters.get('min_want_count', 0)
            if min_want > 0:
                filtered_data = [item for item in filtered_data if item.get('wantCount', 0) >= min_want]
            
            # 价格范围过滤
            min_price = filters.get('min_price', 0)
            max_price = filters.get('max_price', 0)
            if min_price > 0:
                filtered_data = [item for item in filtered_data if item.get('price', 0) >= min_price]
            if max_price > 0:
                filtered_data = [item for item in filtered_data if item.get('price', 0) <= max_price]
            
            # 状态过滤
            status_filter = filters.get('status')
            if status_filter:
                filtered_data = [item for item in filtered_data if item.get('status') == status_filter]
            
            # 关键词过滤
            keyword = filters.get('keyword', '').strip()
            if keyword:
                filtered_data = [
                    item for item in filtered_data 
                    if keyword.lower() in item.get('title', '').lower()
                ]
            
            self.logger.info(f"数据过滤完成，剩余{len(filtered_data)}条记录")
            return filtered_data
            
        except Exception as e:
            self.logger.error(f"数据过滤失败: {e}")
            return data
    
    def get_statistics(self, data: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        获取数据统计信息
        
        Args:
            data: 数据列表
        
        Returns:
            Dict: 统计信息
        """
        if not data:
            return {
                'total_count': 0,
                'avg_price': 0,
                'avg_want_count': 0,
                'avg_browse_count': 0,
                'avg_conversion_rate': 0,
                'sold_count': 0,
                'available_count': 0
            }
        
        total_count = len(data)
        prices = [item.get('price', 0) for item in data]
        want_counts = [item.get('wantCount', 0) for item in data]
        browse_counts = [item.get('browseCnt', 0) for item in data]
        conversion_rates = [item.get('conversionRate', 0) for item in data]
        
        sold_count = len([item for item in data if item.get('status') == '已售出'])
        available_count = total_count - sold_count
        
        return {
            'total_count': total_count,
            'avg_price': round(sum(prices) / total_count, 2) if prices else 0,
            'avg_want_count': round(sum(want_counts) / total_count, 1) if want_counts else 0,
            'avg_browse_count': round(sum(browse_counts) / total_count, 1) if browse_counts else 0,
            'avg_conversion_rate': round(sum(conversion_rates) / total_count, 2) if conversion_rates else 0,
            'sold_count': sold_count,
            'available_count': available_count
        }
