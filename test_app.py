#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
XY商品采集器测试脚本
测试核心功能而不启动GUI
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_basic_functionality():
    """测试基础功能"""
    print("🔍 测试基础功能...")
    
    try:
        # 测试日志系统
        from utils.logger import setup_logger
        logger = setup_logger()
        logger.info("日志系统测试成功")
        print("✅ 日志系统正常")
        
        # 测试配置管理
        from core.config_manager import ConfigManager
        config_manager = ConfigManager()
        
        # 测试配置读写
        config_manager.set('test.value', 'hello world')
        test_value = config_manager.get('test.value')
        assert test_value == 'hello world', "配置读写测试失败"
        print("✅ 配置管理正常")
        
        # 测试授权管理
        from core.auth_manager import AuthManager
        auth_manager = AuthManager()
        machine_code = auth_manager.get_machine_code()
        assert len(machine_code) > 0, "机器码生成失败"
        print(f"✅ 授权管理正常 (机器码: {machine_code[:8]}...)")
        
        # 测试数据处理
        from core.data_processor import DataProcessor
        data_processor = DataProcessor()
        
        # 测试数据清洗
        test_data = [
            {
                'itemId': '123',
                'title': '测试商品',
                'price': '99.99',
                'wantCount': '10',
                'browseCnt': '100'
            }
        ]
        cleaned_data = data_processor.clean_and_validate(test_data)
        assert len(cleaned_data) == 1, "数据清洗测试失败"
        assert cleaned_data[0]['conversionRate'] == 10.0, "转化率计算错误"
        print("✅ 数据处理正常")
        
        # 测试导出管理
        from core.export_manager import ExportManager
        export_manager = ExportManager(config_manager)
        
        # 测试HTML生成
        html_content = export_manager._generate_html_content(cleaned_data, "测试数据")
        assert '测试商品' in html_content, "HTML生成测试失败"
        print("✅ 导出管理正常")
        
        return True
        
    except Exception as e:
        print(f"❌ 基础功能测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_data_model():
    """测试数据模型"""
    print("\n🔍 测试数据模型...")
    
    try:
        from core.data_processor import ProductDataModel
        
        # 创建测试数据
        test_data = [
            {
                'itemId': '123',
                'title': '测试商品1',
                'price': 99.99,
                'wantCount': 10,
                'browseCnt': 100,
                'conversionRate': 10.0,
                'status': '在售'
            },
            {
                'itemId': '456', 
                'title': '测试商品2',
                'price': 199.99,
                'wantCount': 20,
                'browseCnt': 200,
                'conversionRate': 10.0,
                'status': '已售出'
            }
        ]
        
        # 创建数据模型
        model = ProductDataModel(test_data)
        
        # 测试基本功能
        assert model.rowCount() == 2, "行数错误"
        assert model.columnCount() == 9, "列数错误"
        
        # 测试数据获取
        item = model.get_item(0)
        assert item['title'] == '测试商品1', "数据获取错误"
        
        print("✅ 数据模型正常")
        return True
        
    except Exception as e:
        print(f"❌ 数据模型测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_file_operations():
    """测试文件操作"""
    print("\n🔍 测试文件操作...")
    
    try:
        from core.export_manager import ExportManager
        from core.config_manager import ConfigManager
        
        config_manager = ConfigManager()
        export_manager = ExportManager(config_manager)
        
        # 测试数据
        test_data = [
            {
                'itemId': '123',
                'title': '测试商品',
                'price': 99.99,
                'wantCount': 10,
                'browseCnt': 100,
                'conversionRate': 10.0,
                'status': '在售',
                'publishTime': '2025-07-30 14:00:00',
                'link': 'https://example.com/item/123'
            }
        ]
        
        # 测试HTML导出
        html_file = export_manager.export_to_html(test_data, "测试HTML导出")
        assert os.path.exists(html_file), "HTML文件创建失败"
        print(f"✅ HTML导出正常: {html_file}")
        
        # 测试Excel导出
        excel_file = export_manager.export_to_excel(test_data, "测试Excel导出")
        assert os.path.exists(excel_file), "Excel文件创建失败"
        print(f"✅ Excel导出正常: {excel_file}")
        
        return True
        
    except Exception as e:
        print(f"❌ 文件操作测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("=" * 60)
    print("🧪 XY商品采集器 v4.0 - 功能测试")
    print("=" * 60)
    
    all_passed = True
    
    # 测试基础功能
    if not test_basic_functionality():
        all_passed = False
    
    # 测试数据模型
    if not test_data_model():
        all_passed = False
    
    # 测试文件操作
    if not test_file_operations():
        all_passed = False
    
    print("\n" + "=" * 60)
    if all_passed:
        print("🎉 所有测试通过！应用程序核心功能正常。")
        print("\n📝 接下来您可以：")
        print("1. 运行 'python main.py' 启动完整应用程序")
        print("2. 运行 'python run_app.py' 启动带检查的应用程序")
        print("3. 安装 selenium 来启用数据采集功能")
        print("   pip install selenium")
    else:
        print("❌ 部分测试失败，请检查错误信息。")
    print("=" * 60)
    
    return 0 if all_passed else 1

if __name__ == "__main__":
    sys.exit(main())
