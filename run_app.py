#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
XY商品采集器启动脚本
用于测试和运行应用程序
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def check_dependencies():
    """检查依赖包"""
    required_packages = [
        'PyQt5',
        'selenium', 
        'requests',
        'openpyxl'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
            print(f"✅ {package} - 已安装")
        except ImportError:
            missing_packages.append(package)
            print(f"❌ {package} - 未安装")
    
    if missing_packages:
        print(f"\n缺少以下依赖包: {', '.join(missing_packages)}")
        print("请运行以下命令安装:")
        print(f"pip install {' '.join(missing_packages)}")
        return False
    
    return True

def test_modules():
    """测试核心模块"""
    try:
        print("\n🔍 测试核心模块...")
        
        # 测试日志模块
        from utils.logger import setup_logger
        logger = setup_logger()
        logger.info("日志模块测试成功")
        print("✅ 日志模块 - 正常")
        
        # 测试配置管理
        from core.config_manager import ConfigManager
        config_manager = ConfigManager()
        print("✅ 配置管理 - 正常")
        
        # 测试授权管理
        from core.auth_manager import AuthManager
        auth_manager = AuthManager()
        machine_code = auth_manager.get_machine_code()
        print(f"✅ 授权管理 - 正常 (机器码: {machine_code[:8]}...)")
        
        # 测试数据处理
        from core.data_processor import DataProcessor
        data_processor = DataProcessor()
        print("✅ 数据处理 - 正常")
        
        # 测试导出管理
        from core.export_manager import ExportManager
        export_manager = ExportManager(config_manager)
        print("✅ 导出管理 - 正常")
        
        return True
        
    except Exception as e:
        print(f"❌ 模块测试失败: {e}")
        return False

def run_gui():
    """运行GUI应用程序"""
    try:
        print("\n🚀 启动GUI应用程序...")
        
        from PyQt5.QtWidgets import QApplication
        from ui.main_window import MainWindow
        from core.auth_manager import AuthManager
        from core.config_manager import ConfigManager
        
        # 创建应用程序
        app = QApplication(sys.argv)
        app.setApplicationName("XY商品采集器")
        app.setApplicationVersion("4.0")
        
        # 创建管理器
        auth_manager = AuthManager()
        config_manager = ConfigManager()
        
        # 创建主窗口
        main_window = MainWindow(auth_manager, config_manager)
        main_window.show()
        
        print("✅ GUI应用程序启动成功")
        print("📝 提示: 关闭窗口退出程序")
        
        # 运行应用程序
        return app.exec_()
        
    except Exception as e:
        print(f"❌ GUI启动失败: {e}")
        import traceback
        traceback.print_exc()
        return 1

def main():
    """主函数"""
    print("=" * 50)
    print("🎯 XY商品采集器 v4.0 - Python版")
    print("=" * 50)
    
    # 检查依赖
    if not check_dependencies():
        return 1
    
    # 测试模块
    if not test_modules():
        return 1
    
    print("\n✅ 所有检查通过，准备启动应用程序...")
    
    # 运行GUI
    return run_gui()

if __name__ == "__main__":
    sys.exit(main())
