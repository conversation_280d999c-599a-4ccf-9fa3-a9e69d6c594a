# XY商品采集器 v4.0 - Python API版本

## 🎯 项目概述

这是XY商品采集器的全新Python版本，采用纯HTTP API请求方式，摆脱了Chrome扩展的限制，提供更稳定、更高效的数据采集体验。

## ✨ 主要特性

### 🚀 技术架构升级
- **纯Python实现**: 不再依赖Chrome扩展和Selenium
- **直接API调用**: 使用闲鱼官方API接口获取数据
- **现代化GUI**: 基于PyQt5的美观用户界面
- **模块化设计**: 清晰的代码结构，易于维护和扩展

### 📊 核心功能
- ✅ **关键词搜索采集**: 支持多页搜索结果采集
- ✅ **店铺商品采集**: 支持指定店铺的商品数据采集
- ✅ **实时数据展示**: 表格形式展示采集结果
- ✅ **多格式导出**: HTML、Excel、图片包等多种导出格式
- ✅ **数据过滤**: 支持按想要人数、价格等条件过滤
- ✅ **进度监控**: 实时显示采集进度和状态

### 🔧 调试模式
- **授权跳过**: 调试期间自动跳过授权验证
- **模拟数据**: 内置测试数据用于功能验证
- **详细日志**: 完整的操作日志记录

## 📁 项目结构

```
XY商品采集插件/
├── core/                          # 核心模块
│   ├── __init__.py
│   ├── auth_manager.py            # 授权管理（调试模式）
│   ├── config_manager.py          # 配置管理
│   ├── collector_engine.py        # API采集引擎 ⭐
│   ├── data_processor.py          # 数据处理
│   └── export_manager.py          # 导出管理
├── ui/                            # 用户界面
│   ├── __init__.py
│   └── main_window.py             # 主窗口界面
├── utils/                         # 工具模块
│   ├── __init__.py
│   └── logger.py                  # 日志系统
├── main.py                        # 主程序入口
├── debug_gui.py                   # GUI调试脚本 ⭐
├── test_api_collector.py          # API测试脚本 ⭐
├── requirements.txt               # 依赖包列表
└── README_API_VERSION.md          # 本文档
```

## 🔌 API接口说明

### 搜索商品API
- **接口**: `https://h5api.m.goofish.com/h5/mtop.taobao.idlemtopsearch.pc.search`
- **方法**: GET
- **参数**: 关键词、页码、页面大小等
- **返回**: `resultList` 包含商品列表数据

### 店铺商品API
- **接口**: `https://h5api.m.goofish.com/h5/mtop.idle.web.xyh.item.list`
- **方法**: GET
- **参数**: 店铺ID、页码、页面大小等
- **返回**: `itemList` 包含店铺商品数据

## 🚀 快速开始

### 1. 环境准备
```bash
# 安装Python依赖
pip install PyQt5 requests openpyxl

# 可选依赖（完整功能）
pip install pandas Pillow
```

### 2. 功能测试
```bash
# 测试API采集引擎
python test_api_collector.py

# 启动GUI调试模式
python debug_gui.py

# 启动完整应用程序
python main.py
```

### 3. 使用说明

#### GUI界面操作
1. **搜索采集**:
   - 输入关键词（如：手机）
   - 设置想要人数过滤条件
   - 设置采集页数
   - 点击"🔍 开始搜索"

2. **店铺采集**:
   - 输入店铺链接（如：https://www.goofish.com/user/123456）
   - 设置过滤条件
   - 点击"🛒 采集店铺"

3. **数据导出**:
   - 点击"📋 详细展示"在浏览器中查看
   - 点击"📦 一键导出"选择导出格式

## 📊 数据字段说明

采集到的商品数据包含以下字段：

| 字段名 | 类型 | 说明 |
|--------|------|------|
| itemId | string | 商品ID |
| title | string | 商品标题 |
| price | float | 商品价格 |
| wantCount | int | 想要人数 |
| browseCnt | int | 浏览次数 |
| conversionRate | float | 转化率（想要/浏览*100） |
| status | string | 商品状态（在售/已售出） |
| publishTime | string | 发布时间 |
| link | string | 商品链接 |
| imageUrl | string | 主图链接 |
| images | array | 所有图片链接 |
| location | string | 商品位置 |
| sellerNick | string | 卖家昵称 |

## 🔧 配置说明

### 配置文件 (config.json)
```json
{
  "collection": {
    "min_want_count": 10,
    "max_pages": 3,
    "detail_mode": false
  },
  "export": {
    "default_path": "exports",
    "auto_open_html": true,
    "include_images": true
  }
}
```

### 日志配置
- 日志文件位置: `logs/xy_collector_YYYYMMDD.log`
- 日志级别: INFO（可在配置中调整）
- 自动轮转: 10MB/文件，保留5个备份

## 🐛 调试功能

### 调试模式特性
- **授权跳过**: 自动跳过授权验证，方便开发测试
- **模拟数据**: 内置3条测试商品数据
- **详细日志**: 所有API请求和响应都有详细记录
- **错误处理**: 友好的错误提示和异常处理

### 测试脚本
1. **test_api_collector.py**: 测试API采集引擎的各项功能
2. **debug_gui.py**: 启动带模拟数据的GUI界面
3. **test_app.py**: 测试所有核心模块的基础功能

## 📈 性能优化

- **请求间隔**: 自动添加1-3秒随机间隔，避免请求过频
- **数据缓存**: 智能缓存机制，减少重复请求
- **内存管理**: 及时清理临时数据，避免内存泄漏
- **错误重试**: 网络异常时自动重试机制

## 🔮 后续计划

### 即将实现
- [ ] 恢复完整的授权验证系统
- [ ] 添加更多数据过滤选项
- [ ] 支持自定义导出模板
- [ ] 添加数据统计分析功能

### 长期规划
- [ ] 支持更多电商平台
- [ ] 添加数据可视化图表
- [ ] 实现定时采集任务
- [ ] 云端数据同步功能

## 📞 技术支持

如果您在使用过程中遇到问题，请：

1. 查看日志文件获取详细错误信息
2. 运行测试脚本验证功能状态
3. 检查网络连接和API可用性

## 📄 更新日志

### v4.0.0 (2025-07-30)
- ✨ 全新的Python API版本
- 🚀 直接HTTP请求，摆脱浏览器依赖
- 🎨 现代化PyQt5界面
- 📊 完整的数据处理和导出功能
- 🔧 调试模式，方便开发测试

---

**注意**: 当前版本为调试版本，授权验证功能已临时禁用。正式使用前请联系开发者恢复授权功能。
