#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试新的API采集引擎
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_search_collection():
    """测试搜索采集功能"""
    print("🔍 测试搜索采集功能...")
    
    try:
        from core.collector_engine import CollectorEngine
        from core.config_manager import ConfigManager
        
        # 创建配置管理器和采集引擎
        config_manager = ConfigManager()
        collector = CollectorEngine(config_manager)
        
        # 定义进度回调
        def progress_callback(info):
            print(f"📊 进度: 第{info['current_page']}/{info['total_pages']}页, "
                  f"当前页{info['current_items']}条, 总计{info['total_items']}条")
        
        # 测试搜索采集
        print("🚀 开始测试搜索: '手机'")
        results = collector.collect_search_data(
            keyword="手机",
            max_pages=2,
            min_want_count=5,
            progress_callback=progress_callback
        )
        
        print(f"✅ 搜索采集完成，获得 {len(results)} 条数据")
        
        # 显示前3条数据
        for i, item in enumerate(results[:3], 1):
            print(f"\n📱 商品 {i}:")
            print(f"   标题: {item.get('title', '')[:50]}...")
            print(f"   价格: ¥{item.get('price', 0)}")
            print(f"   想要: {item.get('wantCount', 0)} 人")
            print(f"   浏览: {item.get('browseCnt', 0)} 次")
            print(f"   转化率: {item.get('conversionRate', 0)}%")
            print(f"   状态: {item.get('status', '')}")
        
        return len(results) > 0
        
    except Exception as e:
        print(f"❌ 搜索采集测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_api_params():
    """测试API参数构建"""
    print("\n🔧 测试API参数构建...")
    
    try:
        from core.collector_engine import CollectorEngine
        from core.config_manager import ConfigManager
        
        config_manager = ConfigManager()
        collector = CollectorEngine(config_manager)
        
        # 测试搜索参数
        search_params = collector._build_search_params("测试关键词", 1)
        print("✅ 搜索参数构建成功")
        print(f"   API: {search_params.get('api')}")
        print(f"   关键词: 测试关键词")
        print(f"   时间戳: {search_params.get('t')}")
        
        # 测试店铺参数
        shop_params = collector._build_shop_params("123456", 1)
        print("✅ 店铺参数构建成功")
        print(f"   API: {shop_params.get('api')}")
        print(f"   店铺ID: 123456")
        
        return True
        
    except Exception as e:
        print(f"❌ API参数测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_data_processing():
    """测试数据处理功能"""
    print("\n📊 测试数据处理功能...")
    
    try:
        from core.collector_engine import CollectorEngine
        from core.config_manager import ConfigManager
        
        config_manager = ConfigManager()
        collector = CollectorEngine(config_manager)
        
        # 模拟API返回的数据
        mock_items = [
            {
                'itemId': '123456',
                'title': '测试商品1',
                'price': '99.99',
                'wantCnt': '10',
                'browseCnt': '100',
                'isSold': False,
                'picUrl': 'https://example.com/pic1.jpg',
                'proPolishTime': '1640995200000',  # 2022-01-01 00:00:00
                'location': '北京',
                'sellerNick': '测试卖家'
            },
            {
                'itemId': '789012',
                'title': '测试商品2',
                'price': '199.99',
                'wantCnt': '20',
                'browseCnt': '200',
                'isSold': True,
                'picUrl': 'https://example.com/pic2.jpg',
                'proPolishTime': '1640995200000',
                'location': '上海',
                'sellerNick': '测试卖家2'
            }
        ]
        
        # 处理数据
        processed_items = collector._process_search_items(mock_items)
        
        print(f"✅ 数据处理成功，处理了 {len(processed_items)} 条数据")
        
        for i, item in enumerate(processed_items, 1):
            print(f"\n📦 处理后的商品 {i}:")
            print(f"   ID: {item.get('itemId')}")
            print(f"   标题: {item.get('title')}")
            print(f"   价格: ¥{item.get('price')}")
            print(f"   想要: {item.get('wantCount')} 人")
            print(f"   浏览: {item.get('browseCnt')} 次")
            print(f"   转化率: {item.get('conversionRate')}%")
            print(f"   状态: {item.get('status')}")
            print(f"   发布时间: {item.get('publishTime')}")
            print(f"   链接: {item.get('link')}")
        
        return len(processed_items) == 2
        
    except Exception as e:
        print(f"❌ 数据处理测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_shop_url_parsing():
    """测试店铺URL解析"""
    print("\n🏪 测试店铺URL解析...")
    
    try:
        from core.collector_engine import CollectorEngine
        from core.config_manager import ConfigManager
        
        config_manager = ConfigManager()
        collector = CollectorEngine(config_manager)
        
        # 测试不同格式的店铺URL
        test_urls = [
            "https://www.goofish.com/user/123456",
            "https://www.goofish.com/shop?userId=789012",
            "https://m.goofish.com/user/456789"
        ]
        
        for url in test_urls:
            shop_id = collector._extract_shop_id_from_url(url)
            print(f"   URL: {url}")
            print(f"   提取的店铺ID: {shop_id}")
        
        return True
        
    except Exception as e:
        print(f"❌ 店铺URL解析测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("=" * 60)
    print("🧪 XY商品采集器 - API采集引擎测试")
    print("=" * 60)
    
    all_passed = True
    
    # 测试API参数构建
    if not test_api_params():
        all_passed = False
    
    # 测试数据处理
    if not test_data_processing():
        all_passed = False
    
    # 测试店铺URL解析
    if not test_shop_url_parsing():
        all_passed = False
    
    # 测试实际搜索采集（需要网络连接）
    print("\n❓ 是否测试实际的网络采集？(y/n): ", end="")
    try:
        response = input().lower().strip()
        if response in ['y', 'yes', '是']:
            if not test_search_collection():
                all_passed = False
        else:
            print("⏭️ 跳过网络采集测试")
    except KeyboardInterrupt:
        print("\n⏭️ 跳过网络采集测试")
    
    print("\n" + "=" * 60)
    if all_passed:
        print("🎉 所有测试通过！新的API采集引擎工作正常。")
        print("\n📝 接下来您可以：")
        print("1. 运行 'python debug_gui.py' 测试GUI界面")
        print("2. 运行 'python main.py' 启动完整应用程序")
        print("3. 在GUI中测试搜索和店铺采集功能")
    else:
        print("❌ 部分测试失败，请检查错误信息。")
    print("=" * 60)
    
    return 0 if all_passed else 1

if __name__ == "__main__":
    sys.exit(main())
