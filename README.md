# XY爆款数据采集器 V3.0 - 加密版

> 专业的闲鱼商品数据采集浏览器扩展插件 - 带授权验证系统

## 🌟 功能特色

### 🔐 授权验证系统
- **安全授权**：使用前需要输入有效的授权卡密
- **机器码绑定**：每个授权卡密绑定特定设备
- **在线验证**：优先使用网络验证，支持离线备用验证
- **自动保存**：验证成功后自动保存，下次免输入

### 📊 双模式采集
- **快速模式**：采集基础商品信息（标题、价格、想要人数、状态、链接）
- **详细模式**：采集完整商品数据（包含浏览量、转化比、完整标题、商品描述、配图等）

### 🎯 智能采集
- **搜索商品采集**：支持多页采集，可设置最小想要人数过滤
- **店铺商品采集**：支持指定数量采集，深度挖掘店铺爆款
- **实时进度显示**：美观的悬浮窗实时显示采集进度和状态

### 📈 数据分析
- **转化比计算**：自动计算想要人数/浏览量转化比
- **数据排序**：支持想要人数、浏览量、转化比三列排序
- **状态识别**：自动识别商品在售/已售出状态

### 💾 数据导出
- **HTML可视化**：生成美观的HTML表格，支持排序和详情查看
- **一键导出功能**：HTML内置三种导出方式（图片包、Excel、完整包）
- **智能格式识别**：根据采集模式自动调整导出内容和格式
- **多种导出选择**：纯图片、纯数据、完整数据包任选其一
- **便于分析**：Excel格式便于数据分析，图片包便于素材使用

## 🚀 使用方法

### 安装步骤
1. 下载插件文件到本地
2. 打开Chrome浏览器，进入扩展程序管理页面
3. 开启"开发者模式"
4. 点击"加载已解压的扩展程序"，选择插件文件夹
5. 安装完成后，浏览器工具栏会出现插件图标

### 🔑 首次授权验证
1. 点击插件图标，会弹出现代化的授权验证窗口
2. 系统自动生成并显示机器码，点击"复制"按钮复制机器码
3. 联系客服（QQ: 597882859，备注：闲鱼采集）提供机器码获取授权卡密
4. 在验证窗口输入获得的授权卡密
5. 点击"验证授权"按钮进行在线验证
6. 验证成功后显示绿色成功提示，自动跳转到主功能界面
7. 下次使用时会自动登录，无需重复验证

### 搜索商品采集
1. 在闲鱼网站搜索商品，进入搜索结果页面
2. 点击插件图标，展开"搜索商品专区"
3. 设置采集参数：
   - **想要人数过滤**：只采集想要人数≥设定值的商品
   - **采集页数**：设置要采集的页数（最大50页）
   - **详细采集**：勾选后获取完整商品数据
   - **采集间隔**：详细采集时每个商品的间隔时间
4. 点击"开始采集"，等待采集完成
5. 点击"导出数据"下载HTML文件
6. 打开HTML文件，使用内置的导出功能：
   - **📁 导出图片压缩包**：下载所有商品图片
   - **📊 导出Excel表格**：下载商品数据表格
   - **📦 导出完整数据包**：同时下载图片和数据

### 店铺商品采集
1. 进入任意闲鱼店铺页面
2. 点击插件图标，展开"店铺商品专区"
3. 设置采集参数：
   - **采集数量**：选择采集全部或指定数量
   - **想要人数过滤**：只采集想要人数≥设定值的商品
   - **详细采集**：勾选后获取完整商品数据
   - **采集间隔**：详细采集时每个商品的间隔时间
4. 点击"采集店铺商品"，等待采集完成
5. 点击"导出店铺数据"下载HTML文件
6. 打开HTML文件，使用内置的导出功能：
   - **📁 导出图片压缩包**：下载所有商品图片
   - **📊 导出Excel表格**：下载商品数据表格
   - **📦 导出完整数据包**：同时下载图片和数据

## 🎨 界面特色

### 现代化设计
- **简洁界面**：干净的白色卡片式设计，告别复杂的背景
- **蓝色主题**：统一的蓝色渐变主题，视觉协调美观
- **圆角设计**：现代化的圆角元素，柔和舒适
- **响应式交互**：悬停效果和状态反馈，提升用户体验
- **智能布局**：自适应布局，确保在不同状态下都保持美观

### 用户体验
- **折叠面板**：节省空间的可折叠区域
- **实时通知**：操作结果的即时反馈，胶囊式消息提示
- **进度显示**：详细的采集进度和统计
- **设置记忆**：自动保存用户配置
- **稳定布局**：验证消息采用固定高度设计，避免界面跳动
- **一键复制**：机器码一键复制功能，操作便捷

## 📋 导出数据格式

### HTML表格结构
```
序号 | 商品图片 | 商品标题 | 商品价格 | 想要人数↕ | 浏览量↕ | 转化比↕ | 状态 | 商品链接 | 操作
```

### 一键导出功能
- **📁 图片压缩包**：`XY商品图片_日期.zip`
  ```
  XY商品图片_2024-01-01.zip
  └── 商品图片/
      ├── iPhone 15 Pro Max 256GB/
      │   ├── 主图.jpg
      │   ├── 详情图_1.jpg
      │   └── 详情图_2.jpg
      ├── MacBook Air M2 13英寸/
      │   ├── 主图.jpg
      │   └── 详情图_1.jpg
      └── AirPods Pro 第三代/
          ├── 主图.jpg
          ├── 详情图_1.jpg
          └── 详情图_2.jpg
  ```
- **📊 Excel表格**：`XY商品数据_日期.xlsx`
  - 基础模式：9列数据（序号、标题、价格、想要人数、浏览量、转化比、状态、商品链接、主图链接）
  - 详细模式：12列数据（增加原价、商品描述、详情图片）
- **📦 完整数据包**：`XY商品完整数据包_日期.zip`
  - 包含商品图片文件夹和Excel数据文件

### 文件命名规则
- 搜索采集：`XY商品数据（搜索商品采集-详细模式）`
- 店铺采集：`XY商品数据（店铺商品采集-快速模式）`

### 数据字段说明
- **基础数据**：标题、价格、想要人数、状态、链接、图片
- **详细数据**：完整标题、浏览量、转化比、商品描述、完整配图
- **计算字段**：转化比 = 想要人数 / 浏览量 × 100%

## ⚙️ 技术特点

### 核心技术
- **Manifest V3**：使用最新的Chrome扩展API
- **异步采集**：Promise-based异步数据采集
- **DOM解析**：多重选择器确保数据准确性
- **一键导出**：集成JSZip和XLSX库，支持图片和数据导出
- **错误处理**：完善的异常处理和用户反馈

### 性能优化
- **智能等待**：页面加载状态检测
- **内存管理**：及时清理临时数据
- **批量处理**：高效的数据批量操作
- **缓存机制**：本地存储用户配置

### 安全特性
- **权限最小化**：只请求必要的浏览器权限
- **数据隔离**：采集数据本地存储，不上传
- **错误恢复**：采集中断后可继续操作

## 🔧 配置选项

### 采集参数
- **想要人数过滤**：0-999999（默认10）
- **采集页数**：1-50页（默认1页）
- **详细采集间隔**：1-10秒（默认2秒）
- **采集数量**：全部/10/20/50/100条

### 高级设置
- **自动保存配置**：记住用户设置
- **实时进度显示**：采集过程可视化
- **错误重试机制**：网络异常自动重试
- **数据完整性检查**：确保导出数据准确

## 📞 技术支持

- **官方网站**：[网赚库 - wangzhuanku.com](https://www.wangzhuanku.com)
- **详细教程**：[使用教程](https://www.wangzhuanku.com/2025/05/26/xycj/)
- **版本信息**：V3.1
- **更新日期**：2025年7月

## 📄 版权声明

本插件由网赚库开发，仅供学习和研究使用。请遵守相关网站的使用条款，合理使用数据采集功能。

## 🔧 最新更新 (V3.1)

### 🎨 授权界面全面重构 (V3.1)
- **现代化设计**：全新的白色卡片式授权界面，告别复杂的紫色背景
- **蓝色主题统一**：采用统一的蓝色渐变主题，与主功能界面保持一致
- **胶囊式消息**：验证状态消息采用胶囊式设计，单行显示，避免布局跳动
- **智能布局**：固定高度的消息容器，确保界面稳定性
- **优化交互**：改进按钮悬停效果和状态反馈
- **联系信息优化**：底部联系信息采用卡片式设计，QQ图标更加醒目
- **CSP合规**：完全符合Chrome扩展安全策略，移除所有内联JavaScript
- **代码重构**：分离UI逻辑和授权逻辑，代码结构更加清晰

## 🔧 历史更新 (V3.0)

### 🎯 核心新功能：一键导出图片和文案
- **📁 图片压缩包导出**：一键下载所有商品的主图和详情图，自动打包为ZIP格式
- **📊 Excel表格导出**：将所有商品文案数据导出为Excel文件，支持完整的商品信息
- **📦 完整数据包导出**：同时包含图片文件夹和Excel数据文件的组合包
- **🎨 美观导出界面**：在生成的HTML中新增专业的导出工具栏，三个彩色按钮一目了然
- **📈 实时进度显示**：导出过程中显示详细的进度条和状态信息
- **🔄 智能错误处理**：图片下载失败时自动跳过并统计成功率

### 📊 导出功能特点
- **🆕 按商品分类存储图片**：图片压缩包中每个商品有独立文件夹，文件夹名为商品名称
- **🆕 智能文件夹命名**：自动处理重复商品名称，添加数字后缀避免冲突
- **🆕 简化图片命名**：主图命名为"主图.jpg"，详情图命名为"详情图_1.jpg"等
- **Excel列宽优化**：自动设置合适的列宽，便于查看和编辑
- **模式差异化支持**：详细采集模式包含原价、商品描述、详情图片等额外列
- **跨域图片处理**：支持跨域图片下载，确保图片文件完整性
- **CDN库支持**：使用JSZip和XLSX等专业库，确保导出功能稳定可靠

### 翻页功能重大改进
- **修复翻页问题**：解决了设置采集20页但只采集4页的问题
- **增强翻页机制**：新增4种翻页方式，确保翻页成功率
- **智能重试系统**：翻页失败时自动重试3次，避免因网络波动导致的采集中断
- **页面变化检测**：多维度检测页面变化，提高翻页成功识别率

### 用户体验优化
- **统一完成提示**：修复了店铺采集和搜索采集完成提示不一致的问题
- **双重反馈机制**：现在所有采集完成后都有弹窗提示 + 悬浮窗绿色提示
- **错误处理统一**：采集失败时也提供一致的错误反馈

### 排序功能修复
- **修复排序问题**：解决了想要人数和浏览量排序不生效的问题
- **正则表达式修复**：修复了关键的正则表达式错误 `/[^d.-]/g` → `/[^\d.-]/g`
- **智能排序逻辑**：改进了"无数据"项的排序处理，无数据项排在最后并按想要人数二级排序
- **表头保护**：确保排序时表头行不会消失，保持表格结构完整
- **排序指示器**：修复了排序方向指示器的显示问题

### 翻页机制详解
1. **方法1**：点击下一页按钮（支持多种按钮样式）
2. **方法2**：点击具体页码数字
3. **方法3**：使用页码输入框跳转
4. **方法4**：修改URL参数跳转

### 错误处理优化
- **网络异常处理**：翻页失败时等待3秒后重试
- **页面状态检查**：实时检测页码变化和内容加载
- **智能判断**：区分真正的翻页失败和网络延迟

---

**感谢使用XY爆款数据采集器 V3.1！**

## 📞 购买联系

- **QQ客服**：597882859
- **购买备注**：闲鱼采集
- **服务时间**：9:00-22:00
- **支付方式**：支持微信、支付宝
- **售后服务**：提供完整的使用指导和技术支持

---