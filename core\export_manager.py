#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
导出管理模块
负责数据的多格式导出功能
"""

import os
import json
import zipfile
import tempfile
import webbrowser
from datetime import datetime
from pathlib import Path
from typing import List, Dict, Any, Optional
import requests
from openpyxl import Workbook
from openpyxl.styles import Font, Alignment, PatternFill
from utils.logger import get_logger


class ExportManager:
    """导出管理器"""
    
    def __init__(self, config_manager):
        """
        初始化导出管理器
        
        Args:
            config_manager: 配置管理器
        """
        self.config_manager = config_manager
        self.logger = get_logger(self.__class__.__name__)
        
        # 导出目录
        self.export_dir = Path(config_manager.get('export.default_path', 'exports'))
        self.export_dir.mkdir(exist_ok=True)
        
        # 临时目录
        self.temp_dir = Path(tempfile.gettempdir()) / "xy_collector_temp"
        self.temp_dir.mkdir(exist_ok=True)
    
    def export_to_html(self, data: List[Dict[str, Any]], title: str = "XY商品数据") -> str:
        """
        导出为HTML文件
        
        Args:
            data: 商品数据列表
            title: 标题
        
        Returns:
            str: HTML文件路径
        """
        try:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f"{title}_{timestamp}.html"
            filepath = self.export_dir / filename
            
            html_content = self._generate_html_content(data, title)
            
            with open(filepath, 'w', encoding='utf-8') as f:
                f.write(html_content)
            
            self.logger.info(f"HTML导出成功: {filepath}")
            return str(filepath)
            
        except Exception as e:
            self.logger.error(f"HTML导出失败: {e}")
            raise
    
    def export_to_excel(self, data: List[Dict[str, Any]], title: str = "XY商品数据") -> str:
        """
        导出为Excel文件
        
        Args:
            data: 商品数据列表
            title: 标题
        
        Returns:
            str: Excel文件路径
        """
        try:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f"{title}_{timestamp}.xlsx"
            filepath = self.export_dir / filename
            
            # 创建工作簿
            wb = Workbook()
            ws = wb.active
            ws.title = "商品数据"
            
            # 设置表头
            headers = ["序号", "商品标题", "价格", "想要人数", "浏览量", "转化率", "状态", "发布时间", "商品链接"]
            for col, header in enumerate(headers, 1):
                cell = ws.cell(row=1, column=col, value=header)
                cell.font = Font(bold=True, color="FFFFFF")
                cell.fill = PatternFill(start_color="366092", end_color="366092", fill_type="solid")
                cell.alignment = Alignment(horizontal="center")
            
            # 填充数据
            for row, item in enumerate(data, 2):
                ws.cell(row=row, column=1, value=row-1)  # 序号
                ws.cell(row=row, column=2, value=item.get('title', ''))  # 标题
                ws.cell(row=row, column=3, value=item.get('price', 0))  # 价格
                ws.cell(row=row, column=4, value=item.get('wantCount', 0))  # 想要人数
                ws.cell(row=row, column=5, value=item.get('browseCnt', 0))  # 浏览量
                ws.cell(row=row, column=6, value=f"{item.get('conversionRate', 0):.1f}%")  # 转化率
                ws.cell(row=row, column=7, value=item.get('status', '在售'))  # 状态
                ws.cell(row=row, column=8, value=item.get('publishTime', ''))  # 发布时间
                ws.cell(row=row, column=9, value=item.get('link', ''))  # 链接
            
            # 调整列宽
            column_widths = [8, 50, 12, 12, 12, 12, 10, 20, 15]
            for col, width in enumerate(column_widths, 1):
                ws.column_dimensions[ws.cell(row=1, column=col).column_letter].width = width
            
            # 保存文件
            wb.save(filepath)
            
            self.logger.info(f"Excel导出成功: {filepath}")
            return str(filepath)
            
        except Exception as e:
            self.logger.error(f"Excel导出失败: {e}")
            raise
    
    def export_images(self, data: List[Dict[str, Any]], title: str = "XY商品图片") -> str:
        """
        导出商品图片压缩包
        
        Args:
            data: 商品数据列表
            title: 标题
        
        Returns:
            str: 压缩包文件路径
        """
        try:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f"{title}_{timestamp}.zip"
            filepath = self.export_dir / filename
            
            with zipfile.ZipFile(filepath, 'w', zipfile.ZIP_DEFLATED) as zipf:
                for i, item in enumerate(data, 1):
                    try:
                        # 获取图片URL列表
                        images = item.get('images', [])
                        if not images and item.get('imageUrl'):
                            images = [item.get('imageUrl')]
                        
                        if not images:
                            continue
                        
                        # 下载并添加图片到压缩包
                        item_title = self._clean_filename(item.get('title', f'商品{i}'))
                        
                        for j, img_url in enumerate(images[:5]):  # 最多5张图片
                            if not img_url:
                                continue
                            
                            try:
                                # 下载图片
                                response = requests.get(img_url, timeout=10)
                                if response.status_code == 200:
                                    # 确定文件扩展名
                                    ext = self._get_image_extension(img_url, response.headers.get('content-type', ''))
                                    
                                    # 添加到压缩包
                                    img_filename = f"{i:03d}_{item_title}_{j+1}{ext}"
                                    zipf.writestr(img_filename, response.content)
                                    
                            except Exception as e:
                                self.logger.warning(f"下载图片失败 {img_url}: {e}")
                                continue
                                
                    except Exception as e:
                        self.logger.warning(f"处理商品图片失败 {item.get('title', '')}: {e}")
                        continue
            
            self.logger.info(f"图片导出成功: {filepath}")
            return str(filepath)
            
        except Exception as e:
            self.logger.error(f"图片导出失败: {e}")
            raise
    
    def export_complete_package(self, data: List[Dict[str, Any]], title: str = "XY商品完整数据") -> str:
        """
        导出完整数据包（HTML + Excel + 图片）
        
        Args:
            data: 商品数据列表
            title: 标题
        
        Returns:
            str: 压缩包文件路径
        """
        try:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f"{title}_{timestamp}_完整包.zip"
            filepath = self.export_dir / filename
            
            # 生成各种格式的文件
            html_file = self.export_to_html(data, title)
            excel_file = self.export_to_excel(data, title)
            
            with zipfile.ZipFile(filepath, 'w', zipfile.ZIP_DEFLATED) as zipf:
                # 添加HTML和Excel文件
                zipf.write(html_file, Path(html_file).name)
                zipf.write(excel_file, Path(excel_file).name)
                
                # 添加图片
                img_dir = "images"
                for i, item in enumerate(data, 1):
                    try:
                        images = item.get('images', [])
                        if not images and item.get('imageUrl'):
                            images = [item.get('imageUrl')]
                        
                        if not images:
                            continue
                        
                        item_title = self._clean_filename(item.get('title', f'商品{i}'))
                        
                        for j, img_url in enumerate(images[:5]):
                            if not img_url:
                                continue
                            
                            try:
                                response = requests.get(img_url, timeout=10)
                                if response.status_code == 200:
                                    ext = self._get_image_extension(img_url, response.headers.get('content-type', ''))
                                    img_filename = f"{img_dir}/{i:03d}_{item_title}_{j+1}{ext}"
                                    zipf.writestr(img_filename, response.content)
                                    
                            except Exception as e:
                                self.logger.warning(f"下载图片失败 {img_url}: {e}")
                                continue
                                
                    except Exception as e:
                        self.logger.warning(f"处理商品图片失败: {e}")
                        continue
            
            # 清理临时文件
            try:
                os.remove(html_file)
                os.remove(excel_file)
            except:
                pass
            
            self.logger.info(f"完整包导出成功: {filepath}")
            return str(filepath)
            
        except Exception as e:
            self.logger.error(f"完整包导出失败: {e}")
            raise
    
    def open_in_browser(self, filepath: str) -> bool:
        """
        在浏览器中打开文件
        
        Args:
            filepath: 文件路径
        
        Returns:
            bool: 是否成功打开
        """
        try:
            webbrowser.open(f'file://{os.path.abspath(filepath)}')
            self.logger.info(f"已在浏览器中打开: {filepath}")
            return True
        except Exception as e:
            self.logger.error(f"打开浏览器失败: {e}")
            return False
    
    def _generate_html_content(self, data: List[Dict[str, Any]], title: str) -> str:
        """生成HTML内容"""
        # 这里复用content.js中的HTML模板逻辑
        html_template = """
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{title}</title>
    <style>
        body {{ font-family: 'Microsoft YaHei', Arial, sans-serif; margin: 20px; background-color: #f5f5f5; }}
        .container {{ max-width: 1200px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }}
        h1 {{ color: #2c3e50; text-align: center; margin-bottom: 30px; }}
        .stats {{ background: #ecf0f1; padding: 15px; border-radius: 5px; margin-bottom: 20px; }}
        table {{ width: 100%; border-collapse: collapse; margin-top: 20px; }}
        th, td {{ border: 1px solid #ddd; padding: 12px; text-align: left; }}
        th {{ background-color: #34495e; color: white; font-weight: bold; }}
        tr:nth-child(even) {{ background-color: #f9f9f9; }}
        tr:hover {{ background-color: #e8f4f8; }}
        .price {{ color: #e74c3c; font-weight: bold; }}
        .status-sold {{ color: #95a5a6; }}
        .status-available {{ color: #27ae60; }}
        .conversion-rate {{ font-weight: bold; }}
        .export-buttons {{ margin: 20px 0; text-align: center; }}
        .export-btn {{ background: #3498db; color: white; padding: 10px 20px; margin: 0 10px; border: none; border-radius: 5px; cursor: pointer; }}
        .export-btn:hover {{ background: #2980b9; }}
        .footer {{ text-align: center; margin-top: 30px; color: #7f8c8d; font-size: 12px; }}
    </style>
</head>
<body>
    <div class="container">
        <h1>{title}</h1>
        <div class="stats">
            <strong>采集统计：</strong>共 {total_count} 条商品数据 | 
            在售：{available_count} 条 | 已售：{sold_count} 条 | 
            平均价格：¥{avg_price:.2f} | 采集时间：{timestamp}
        </div>
        
        <div class="export-buttons">
            <button class="export-btn" onclick="exportToExcel()">📊 导出Excel</button>
            <button class="export-btn" onclick="exportImages()">📁 导出图片包</button>
            <button class="export-btn" onclick="exportComplete()">📦 导出完整包</button>
        </div>
        
        <table>
            <thead>
                <tr>
                    <th>序号</th>
                    <th>商品标题</th>
                    <th>价格</th>
                    <th>想要人数</th>
                    <th>浏览量</th>
                    <th>转化率</th>
                    <th>状态</th>
                    <th>发布时间</th>
                    <th>操作</th>
                </tr>
            </thead>
            <tbody>
                {table_rows}
            </tbody>
        </table>
        
        <div class="footer">
            XY商品采集器 v4.0 - Python版 | 数据仅供参考
        </div>
    </div>
    
    <script>
        function exportToExcel() {{
            alert('请使用主程序的导出功能');
        }}
        function exportImages() {{
            alert('请使用主程序的导出功能');
        }}
        function exportComplete() {{
            alert('请使用主程序的导出功能');
        }}
    </script>
</body>
</html>
        """
        
        # 生成表格行
        table_rows = ""
        for i, item in enumerate(data, 1):
            status_class = "status-sold" if item.get('status') == '已售出' else "status-available"
            conversion_rate = item.get('conversionRate', 0)
            
            table_rows += f"""
                <tr>
                    <td>{i}</td>
                    <td>{item.get('title', '')}</td>
                    <td class="price">¥{item.get('price', 0):.2f}</td>
                    <td>{item.get('wantCount', 0)}</td>
                    <td>{item.get('browseCnt', 0)}</td>
                    <td class="conversion-rate">{conversion_rate:.1f}%</td>
                    <td class="{status_class}">{item.get('status', '在售')}</td>
                    <td>{item.get('publishTime', '')}</td>
                    <td><a href="{item.get('link', '')}" target="_blank">查看</a></td>
                </tr>
            """
        
        # 计算统计信息
        total_count = len(data)
        available_count = len([item for item in data if item.get('status') != '已售出'])
        sold_count = total_count - available_count
        avg_price = sum(item.get('price', 0) for item in data) / total_count if data else 0
        timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        
        return html_template.format(
            title=title,
            total_count=total_count,
            available_count=available_count,
            sold_count=sold_count,
            avg_price=avg_price,
            timestamp=timestamp,
            table_rows=table_rows
        )
    
    def _clean_filename(self, filename: str) -> str:
        """清理文件名"""
        # 移除不允许的字符
        invalid_chars = '<>:"/\\|?*'
        for char in invalid_chars:
            filename = filename.replace(char, '_')
        
        # 限制长度
        if len(filename) > 50:
            filename = filename[:50]
        
        return filename.strip()
    
    def _get_image_extension(self, url: str, content_type: str) -> str:
        """获取图片扩展名"""
        # 从URL获取
        if '.' in url:
            ext = '.' + url.split('.')[-1].split('?')[0].lower()
            if ext in ['.jpg', '.jpeg', '.png', '.gif', '.webp']:
                return ext
        
        # 从Content-Type获取
        if 'jpeg' in content_type:
            return '.jpg'
        elif 'png' in content_type:
            return '.png'
        elif 'gif' in content_type:
            return '.gif'
        elif 'webp' in content_type:
            return '.webp'
        
        return '.jpg'  # 默认
