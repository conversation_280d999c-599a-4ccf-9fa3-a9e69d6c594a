#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
配置管理模块
负责应用程序配置的读取、保存和管理
"""

import json
import os
from pathlib import Path
from typing import Dict, Any, Optional
from utils.logger import get_logger


class ConfigManager:
    """配置管理器"""
    
    def __init__(self, config_file: str = "config.json"):
        """
        初始化配置管理器
        
        Args:
            config_file: 配置文件名
        """
        self.logger = get_logger(self.__class__.__name__)
        self.config_file = Path(config_file)
        self._config = {}
        self._default_config = {
            # 采集设置
            "collection": {
                "min_want_count": 10,
                "max_pages": 3,
                "detail_mode": False,
                "detail_interval": 2,
                "headless_mode": True
            },
            # 界面设置
            "ui": {
                "window_width": 1200,
                "window_height": 800,
                "theme": "light",
                "auto_save_settings": True
            },
            # 导出设置
            "export": {
                "default_path": "exports",
                "auto_open_html": True,
                "include_images": True,
                "excel_format": "xlsx"
            },
            # 系统设置
            "system": {
                "auto_update_check": True,
                "log_level": "INFO",
                "max_log_files": 5
            }
        }
        self.load_config()
    
    def load_config(self) -> None:
        """加载配置文件"""
        try:
            if self.config_file.exists():
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    self._config = json.load(f)
                self.logger.info(f"配置文件加载成功: {self.config_file}")
            else:
                self.logger.info("配置文件不存在，使用默认配置")
                self._config = self._default_config.copy()
                self.save_config()
        except Exception as e:
            self.logger.error(f"加载配置文件失败: {e}")
            self._config = self._default_config.copy()
    
    def save_config(self) -> bool:
        """保存配置文件"""
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(self._config, f, ensure_ascii=False, indent=2)
            self.logger.info(f"配置文件保存成功: {self.config_file}")
            return True
        except Exception as e:
            self.logger.error(f"保存配置文件失败: {e}")
            return False
    
    def get(self, key: str, default: Any = None) -> Any:
        """
        获取配置值
        
        Args:
            key: 配置键，支持点号分隔的嵌套键
            default: 默认值
        
        Returns:
            配置值
        """
        keys = key.split('.')
        value = self._config
        
        try:
            for k in keys:
                value = value[k]
            return value
        except (KeyError, TypeError):
            return default
    
    def set(self, key: str, value: Any) -> None:
        """
        设置配置值
        
        Args:
            key: 配置键，支持点号分隔的嵌套键
            value: 配置值
        """
        keys = key.split('.')
        config = self._config
        
        # 创建嵌套字典结构
        for k in keys[:-1]:
            if k not in config:
                config[k] = {}
            config = config[k]
        
        config[keys[-1]] = value
        
        # 自动保存
        if self.get('ui.auto_save_settings', True):
            self.save_config()
    
    def get_all(self) -> Dict[str, Any]:
        """获取所有配置"""
        return self._config.copy()
    
    def reset_to_default(self) -> None:
        """重置为默认配置"""
        self._config = self._default_config.copy()
        self.save_config()
        self.logger.info("配置已重置为默认值")
    
    def update(self, config_dict: Dict[str, Any]) -> None:
        """
        批量更新配置
        
        Args:
            config_dict: 配置字典
        """
        def deep_update(base_dict, update_dict):
            for key, value in update_dict.items():
                if isinstance(value, dict) and key in base_dict and isinstance(base_dict[key], dict):
                    deep_update(base_dict[key], value)
                else:
                    base_dict[key] = value
        
        deep_update(self._config, config_dict)
        
        if self.get('ui.auto_save_settings', True):
            self.save_config()
