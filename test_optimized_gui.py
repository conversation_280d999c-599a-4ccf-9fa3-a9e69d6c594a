#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试优化后的GUI功能
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_gui_with_real_search():
    """测试GUI的实际搜索功能"""
    try:
        print("🚀 启动优化后的GUI...")
        
        from PyQt5.QtWidgets import QApplication, QMessageBox
        from ui.main_window import MainWindow
        from core.auth_manager import AuthManager
        from core.config_manager import ConfigManager
        
        # 创建应用程序
        app = QApplication(sys.argv)
        app.setApplicationName("XY商品采集器 - 优化版")
        app.setApplicationVersion("4.0-OPTIMIZED")
        
        # 创建管理器
        auth_manager = AuthManager()
        config_manager = ConfigManager()
        
        # 创建主窗口
        main_window = MainWindow(auth_manager, config_manager)
        
        # 显示窗口
        main_window.show()
        
        # 显示使用说明
        QMessageBox.information(
            main_window, 
            "优化版功能说明", 
            f"🎉 XY商品采集器优化版已启动！\n\n"
            f"✨ 新功能特性：\n"
            f"• 移除了图片列，界面更简洁\n"
            f"• 输入关键词后直接开始采集\n"
            f"• 实时显示采集进度和状态\n"
            f"• 更友好的错误提示信息\n"
            f"• 采集完成后自动弹出结果提示\n\n"
            f"🔧 调试模式：\n"
            f"• 授权验证已跳过\n"
            f"• 可以直接测试搜索功能\n\n"
            f"📝 使用方法：\n"
            f"1. 在关键词框输入搜索词（如：手机）\n"
            f"2. 设置想要人数和页数\n"
            f"3. 点击'🔍 开始搜索'按钮\n"
            f"4. 等待采集完成查看结果\n\n"
            f"💡 提示：关闭此对话框开始使用"
        )
        
        print("✅ GUI启动成功")
        print("📝 功能优化说明:")
        print("   - 移除图片列，界面更简洁")
        print("   - 搜索直接开始，无需额外确认")
        print("   - 实时进度显示")
        print("   - 友好的错误提示")
        print("   - 采集完成自动提示")
        print("\n🔧 调试模式已启用，可以直接测试搜索功能")
        print("📱 建议测试关键词：手机、电脑、书籍等")
        
        # 运行应用程序
        return app.exec_()
        
    except Exception as e:
        print(f"❌ GUI启动失败: {e}")
        import traceback
        traceback.print_exc()
        return 1

def test_data_model():
    """测试优化后的数据模型"""
    try:
        print("\n🔍 测试优化后的数据模型...")
        
        from core.data_processor import ProductDataModel
        
        # 创建测试数据
        test_data = [
            {
                'itemId': '123456',
                'title': '苹果iPhone 15 Pro Max 256GB 深空黑色',
                'price': 8999.00,
                'wantCount': 156,
                'browseCnt': 2341,
                'conversionRate': 6.7,
                'status': '在售',
                'publishTime': '2025-07-30 10:30:00',
                'link': 'https://www.goofish.com/item/123456'
            },
            {
                'itemId': '789012',
                'title': '华为Mate60 Pro 512GB 雅川青',
                'price': 6999.00,
                'wantCount': 89,
                'browseCnt': 1205,
                'conversionRate': 7.4,
                'status': '已售出',
                'publishTime': '2025-07-30 09:15:00',
                'link': 'https://www.goofish.com/item/789012'
            }
        ]
        
        # 创建数据模型
        model = ProductDataModel(test_data)
        
        print(f"✅ 数据模型创建成功")
        print(f"   行数: {model.rowCount()}")
        print(f"   列数: {model.columnCount()}")
        
        # 测试表头
        headers = []
        for i in range(model.columnCount()):
            header = model.headerData(i, 1, 0)  # Qt.Horizontal, Qt.DisplayRole
            headers.append(header)
        
        print(f"   表头: {headers}")
        
        # 验证没有图片列
        assert "图片" not in headers, "表头中不应包含图片列"
        assert "标题" in headers, "表头中应包含标题列"
        
        print("✅ 数据模型优化验证通过")
        return True
        
    except Exception as e:
        print(f"❌ 数据模型测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("=" * 60)
    print("🧪 XY商品采集器 - 优化版GUI测试")
    print("=" * 60)
    
    all_passed = True
    
    # 测试数据模型
    if not test_data_model():
        all_passed = False
    
    print("\n" + "=" * 60)
    if all_passed:
        print("🎉 基础测试通过！启动优化版GUI...")
        print("\n📝 优化内容：")
        print("✅ 移除图片列，界面更简洁")
        print("✅ 搜索功能直接开始采集")
        print("✅ 实时进度显示和状态更新")
        print("✅ 友好的错误提示信息")
        print("✅ 采集完成自动结果提示")
        print("\n🚀 正在启动GUI...")
        
        # 启动GUI
        return test_gui_with_real_search()
    else:
        print("❌ 基础测试失败，请检查错误信息。")
        return 1

if __name__ == "__main__":
    sys.exit(main())
