
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试HTML导出</title>
    <style>
        body { font-family: 'Microsoft YaHei', Arial, sans-serif; margin: 20px; background-color: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        h1 { color: #2c3e50; text-align: center; margin-bottom: 30px; }
        .stats { background: #ecf0f1; padding: 15px; border-radius: 5px; margin-bottom: 20px; }
        table { width: 100%; border-collapse: collapse; margin-top: 20px; }
        th, td { border: 1px solid #ddd; padding: 12px; text-align: left; }
        th { background-color: #34495e; color: white; font-weight: bold; }
        tr:nth-child(even) { background-color: #f9f9f9; }
        tr:hover { background-color: #e8f4f8; }
        .price { color: #e74c3c; font-weight: bold; }
        .status-sold { color: #95a5a6; }
        .status-available { color: #27ae60; }
        .conversion-rate { font-weight: bold; }
        .export-buttons { margin: 20px 0; text-align: center; }
        .export-btn { background: #3498db; color: white; padding: 10px 20px; margin: 0 10px; border: none; border-radius: 5px; cursor: pointer; }
        .export-btn:hover { background: #2980b9; }
        .footer { text-align: center; margin-top: 30px; color: #7f8c8d; font-size: 12px; }
    </style>
</head>
<body>
    <div class="container">
        <h1>测试HTML导出</h1>
        <div class="stats">
            <strong>采集统计：</strong>共 1 条商品数据 | 
            在售：1 条 | 已售：0 条 | 
            平均价格：¥99.99 | 采集时间：2025-07-30 14:44:59
        </div>
        
        <div class="export-buttons">
            <button class="export-btn" onclick="exportToExcel()">📊 导出Excel</button>
            <button class="export-btn" onclick="exportImages()">📁 导出图片包</button>
            <button class="export-btn" onclick="exportComplete()">📦 导出完整包</button>
        </div>
        
        <table>
            <thead>
                <tr>
                    <th>序号</th>
                    <th>商品标题</th>
                    <th>价格</th>
                    <th>想要人数</th>
                    <th>浏览量</th>
                    <th>转化率</th>
                    <th>状态</th>
                    <th>发布时间</th>
                    <th>操作</th>
                </tr>
            </thead>
            <tbody>
                
                <tr>
                    <td>1</td>
                    <td>测试商品</td>
                    <td class="price">¥99.99</td>
                    <td>10</td>
                    <td>100</td>
                    <td class="conversion-rate">10.0%</td>
                    <td class="status-available">在售</td>
                    <td>2025-07-30 14:00:00</td>
                    <td><a href="https://example.com/item/123" target="_blank">查看</a></td>
                </tr>
            
            </tbody>
        </table>
        
        <div class="footer">
            XY商品采集器 v4.0 - Python版 | 数据仅供参考
        </div>
    </div>
    
    <script>
        function exportToExcel() {
            alert('请使用主程序的导出功能');
        }
        function exportImages() {
            alert('请使用主程序的导出功能');
        }
        function exportComplete() {
            alert('请使用主程序的导出功能');
        }
    </script>
</body>
</html>
        