let searchData = []; // 存储搜索页面采集的数据
let shopData = []; // 存储店铺页面采集的数据
let currentItemData = []; // 存储当前商品采集的数据
let isCollecting = false; // 控制采集状态
let shouldStop = false; // 控制停止采集
let isPaused = false; // 控制暂停状态
let enableDetailCollect = false; // 是否启用详细采集
let detailInterval = 3; // 详细采集间隔秒数
let apiDataCache = new Map(); // 缓存API数据

// 从页面中查找API数据获取上架时间
function setupDataExtractor() {
  console.log('🔍 设置数据提取器...');

  // 注入脚本到页面中拦截网络请求
  injectNetworkInterceptor();

  // 立即执行一次
  extractApiDataFromPage();

  // 定期检查页面中的API数据
  setInterval(() => {
    extractApiDataFromPage();
  }, 3000);

  console.log('📊 数据提取器已启动');
}

// 注入网络拦截脚本到页面中
function injectNetworkInterceptor() {
  const script = document.createElement('script');
  script.textContent = `
    (function() {
      console.log('🌐 注入网络拦截器...');

      // 保存原始的fetch函数
      const originalFetch = window.fetch;

      // 重写fetch函数
      window.fetch = async function(...args) {
        const response = await originalFetch.apply(this, args);
        const url = args[0];

        if (typeof url === 'string') {
          // 检查是否是我们需要的API
          if (url.includes('mtop') && (url.includes('feed') || url.includes('detail'))) {
            console.log('🎯 拦截到API请求:', url);

            try {
              const clonedResponse = response.clone();
              const data = await clonedResponse.json();

              // 将数据发送到content script
              window.postMessage({
                type: 'API_DATA_INTERCEPTED',
                url: url,
                data: data
              }, '*');

            } catch (error) {
              console.warn('⚠️ 解析API数据失败:', error);
            }
          }
        }

        return response;
      };

      // 同时拦截XMLHttpRequest
      const originalXHROpen = XMLHttpRequest.prototype.open;
      const originalXHRSend = XMLHttpRequest.prototype.send;

      XMLHttpRequest.prototype.open = function(method, url, ...args) {
        this._url = url;
        return originalXHROpen.apply(this, [method, url, ...args]);
      };

      XMLHttpRequest.prototype.send = function(...args) {
        if (this._url && typeof this._url === 'string') {
          const url = this._url;

          this.addEventListener('load', function() {
            if (url.includes('mtop') && (url.includes('feed') || url.includes('detail'))) {
              console.log('🎯 XHR拦截到API请求:', url);

              try {
                const data = JSON.parse(this.responseText);

                // 将数据发送到content script
                window.postMessage({
                  type: 'API_DATA_INTERCEPTED',
                  url: url,
                  data: data
                }, '*');

              } catch (error) {
                console.warn('⚠️ XHR解析API数据失败:', error);
              }
            }
          });
        }

        return originalXHRSend.apply(this, args);
      };

      console.log('✅ 网络拦截器注入完成');
    })();
  `;

  document.documentElement.appendChild(script);
  script.remove();

  // 监听来自注入脚本的消息
  window.addEventListener('message', (event) => {
    if (event.data && event.data.type === 'API_DATA_INTERCEPTED') {
      console.log('📨 收到拦截的API数据:', event.data);
      processInterceptedApiData(event.data.url, event.data.data);
    }
  });

  console.log('🔌 网络拦截器已注入');
}

// 处理拦截到的API数据
function processInterceptedApiData(url, data) {
  try {
    // 搜索页面API
    if (url.includes('feed') && data && data.data) {
      if (data.data.resultList) {
        console.log('📦 处理搜索页面数据:', data.data.resultList.length, '条');
        data.data.resultList.forEach(item => {
          if (item.itemId && item.proPolishTime) {
            apiDataCache.set('search_' + item.itemId, {
              proPolishTime: item.proPolishTime,
              itemId: item.itemId,
              title: item.title,
              wantCnt: item.wantCnt,
              browseCnt: item.browseCnt
            });
            console.log('✅ 缓存搜索商品 ' + item.itemId + ' 上架时间: ' + new Date(parseInt(item.proPolishTime)).toLocaleString());
          }
        });
      }

      if (data.data.cardList) {
        console.log('📦 处理搜索页面cardList数据:', data.data.cardList.length, '条');
        data.data.cardList.forEach(card => {
          if (card.cardData && card.cardData.attributeMap) {
            const attr = card.cardData.attributeMap;
            const itemId = card.cardData.itemId || attr.itemId;
            const proPolishTime = attr.proPolishTime;

            if (itemId && proPolishTime) {
              apiDataCache.set('search_' + itemId, {
                proPolishTime: proPolishTime,
                itemId: itemId,
                title: card.cardData.title || attr.title,
                wantCnt: card.cardData.wantCnt || attr.wantCnt,
                browseCnt: card.cardData.browseCnt || attr.browseCnt
              });
              console.log('✅ 缓存搜索商品 ' + itemId + ' 上架时间: ' + new Date(parseInt(proPolishTime)).toLocaleString());
            }
          }
        });
      }
    }

    // 详情页API
    if (url.includes('detail') && data && data.data && data.data.itemDO) {
      const item = data.data.itemDO;
      console.log('📦 处理详情页数据:', item.itemId);

      if (item.itemId) {
        apiDataCache.set('detail_' + item.itemId, {
          GMT_CREATE_DATE_KEY: item.GMT_CREATE_DATE_KEY,
          proPolishTime: item.proPolishTime,
          itemId: item.itemId,
          title: item.title,
          wantCnt: item.wantCnt,
          browseCnt: item.browseCnt,
          favCnt: item.favCnt,
          desc: item.desc,
          imageInfos: item.imageInfos
        });

        const timeStr = item.GMT_CREATE_DATE_KEY || (item.proPolishTime ? new Date(parseInt(item.proPolishTime)).toLocaleString() : '未知');
        console.log('✅ 缓存详情商品 ' + item.itemId + ' 上架时间: ' + timeStr);
      }
    }

  } catch (error) {
    console.warn('⚠️ 处理拦截数据失败:', error);
  }
}

// 从页面中提取API数据
function extractApiDataFromPage() {
  try {
    let foundData = false;

    // 方法1: 检查全局变量中的数据
    if (window.__INITIAL_DATA__ && window.__INITIAL_DATA__.data) {
      const data = window.__INITIAL_DATA__.data;
      console.log('🔍 检查__INITIAL_DATA__:', data);

      // 搜索页面数据
      if (data.resultList) {
        console.log('📦 找到搜索页面数据:', data.resultList.length, '条');
        data.resultList.forEach(item => {
          if (item.itemId && item.proPolishTime) {
            apiDataCache.set(`search_${item.itemId}`, {
              proPolishTime: item.proPolishTime,
              itemId: item.itemId,
              title: item.title,
              wantCnt: item.wantCnt,
              browseCnt: item.browseCnt
            });
            foundData = true;
            console.log(`✅ 缓存搜索商品 ${item.itemId} 上架时间: ${new Date(parseInt(item.proPolishTime)).toLocaleString()}`);
          }
        });
      }

      // 详情页数据
      if (data.itemDO) {
        const item = data.itemDO;
        console.log('📦 找到详情页数据:', item);
        if (item.itemId) {
          apiDataCache.set(`detail_${item.itemId}`, {
            GMT_CREATE_DATE_KEY: item.GMT_CREATE_DATE_KEY,
            proPolishTime: item.proPolishTime,
            itemId: item.itemId,
            title: item.title,
            wantCnt: item.wantCnt,
            browseCnt: item.browseCnt,
            favCnt: item.favCnt,
            desc: item.desc,
            imageInfos: item.imageInfos
          });
          foundData = true;
          const timeStr = item.GMT_CREATE_DATE_KEY || (item.proPolishTime ? new Date(parseInt(item.proPolishTime)).toLocaleString() : '未知');
          console.log(`✅ 缓存详情商品 ${item.itemId} 上架时间: ${timeStr}`);
        }
      }
    }

    // 方法2: 检查其他可能的全局变量
    if (window.g_config && window.g_config.data) {
      console.log('🔍 检查g_config:', window.g_config.data);
    }

    // 方法4: 检查window对象上的其他数据
    const windowKeys = Object.keys(window).filter(key =>
      key.includes('data') || key.includes('Data') || key.includes('INITIAL') ||
      key.includes('config') || key.includes('Config')
    );
    if (windowKeys.length > 0) {
      console.log('🔍 发现可能的数据变量:', windowKeys);
      windowKeys.forEach(key => {
        try {
          const data = window[key];
          if (data && typeof data === 'object') {
            console.log(`🔍 检查 window.${key}: `, data);
          }
        } catch (e) {
          // 忽略访问错误
        }
      });
    }

    // 方法3: 检查页面中的script标签中的数据
    const scripts = document.querySelectorAll('script');
    scripts.forEach(script => {
      if (script.textContent && script.textContent.includes('proPolishTime')) {
        console.log('🔍 在script标签中找到proPolishTime数据');
        try {
          // 尝试提取JSON数据
          const matches = script.textContent.match(/\{[^{}]*proPolishTime[^{}]*\}/g);
          if (matches) {
            matches.forEach(match => {
              try {
                const data = JSON.parse(match);
                if (data.itemId && data.proPolishTime) {
                  apiDataCache.set(`script_${data.itemId}`, data);
                  foundData = true;
                  console.log(`✅ 从script提取商品 ${data.itemId} 数据`);
                }
              } catch (e) {
                // 忽略解析错误
              }
            });
          }
        } catch (error) {
          // 忽略错误
        }
      }
    });

    if (foundData) {
      console.log(`📊 数据提取完成，缓存中共有 ${apiDataCache.size} 条记录`);
    }

  } catch (error) {
    console.warn('⚠️ 数据提取失败:', error);
  }
}



// 调试函数：检查API缓存状态
function debugApiCache() {
  console.log('📊 API缓存状态:');
  console.log('缓存条目数量:', apiDataCache.size);

  if (apiDataCache.size > 0) {
    console.log('缓存内容:');
    for (const [key, value] of apiDataCache.entries()) {
      console.log(`${key}: `, value);
    }
  } else {
    console.log('缓存为空');
  }
}

// 将调试函数暴露到全局，方便在控制台调用
window.debugApiCache = debugApiCache;
window.testCurrentItemDetection = function () {
  console.log('🧪 测试当前商品页面检测');
  const result = isItemDetailPage();
  console.log('检测结果:', result);
  return result;
};
window.testCurrentItemCollection = async function () {
  console.log('🧪 测试当前商品采集');
  try {
    const item = await collectCurrentItem();
    console.log('采集结果:', item);
    return item;
  } catch (error) {
    console.error('采集失败:', error);
    return null;
  }
};

// 页面加载时启动数据提取器和悬浮窗
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', () => {
    setupDataExtractor();
    initFloatingWindow();
  });
} else {
  setupDataExtractor();
  initFloatingWindow();
}

// 初始化悬浮窗（页面加载时自动创建，除非用户主动关闭）
function initFloatingWindow() {
  // 延迟一下，确保页面完全加载
  setTimeout(() => {
    // 检查用户是否主动关闭过悬浮窗
    chrome.storage.local.get(['floatingWindowClosed', 'floatingWindowMinimized'], (result) => {
      // 只有用户没有主动关闭过，才自动创建悬浮窗
      if (!result.floatingWindowClosed) {
        // 检查是否在商品详情页
        if (isItemDetailPage()) {
          createCurrentItemFloatingButton();
          console.log('🎛️ 商品详情页快捷采集按钮已创建');
        } else {
          createFloatingWindow();

          // 恢复悬浮窗的最小化状态
          const floatingWindow = document.getElementById('xy-floating-window');
          if (floatingWindow && result.floatingWindowMinimized) {
            floatingWindow.classList.add('minimized');
          }

          console.log('🎛️ 悬浮窗已自动创建并恢复状态');
        }
      } else {
        console.log('🎛️ 用户已主动关闭悬浮窗，不自动创建');
      }
    });
  }, 1000);
}

// 创建商品详情页的快捷采集按钮
function createCurrentItemFloatingButton() {
  // 如果按钮已存在，不重复创建
  if (document.getElementById('xy-current-item-button')) {
    return;
  }

  const button = document.createElement('div');
  button.id = 'xy-current-item-button';
  button.innerHTML = `
    <div class="xy-current-item-content">
      <div class="xy-current-item-icon">📱</div>
      <div class="xy-current-item-text">采集当前商品</div>
    </div>
  `;

  // 添加样式
  const style = document.createElement('style');
  style.textContent = `
    #xy-current-item-button {
      position: fixed;
      top: 50%;
      right: 20px;
      transform: translateY(-50%);
      background: linear-gradient(135deg, #1890ff 0%, #40a9ff 100%);
      color: white;
      border-radius: 25px;
      padding: 12px 20px;
      cursor: pointer;
      z-index: 10000;
      box-shadow: 0 4px 16px rgba(24, 144, 255, 0.3);
      transition: all 0.3s ease;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      user-select: none;
    }

    #xy-current-item-button:hover {
      background: linear-gradient(135deg, #40a9ff 0%, #69c0ff 100%);
      transform: translateY(-50%) scale(1.05);
      box-shadow: 0 6px 20px rgba(24, 144, 255, 0.4);
    }

    #xy-current-item-button:active {
      transform: translateY(-50%) scale(0.95);
    }

    .xy-current-item-content {
      display: flex;
      align-items: center;
      gap: 8px;
    }

    .xy-current-item-icon {
      font-size: 16px;
    }

    .xy-current-item-text {
      font-size: 14px;
      font-weight: 600;
      white-space: nowrap;
    }

    @media (max-width: 768px) {
      #xy-current-item-button {
        right: 10px;
        padding: 10px 16px;
      }

      .xy-current-item-text {
        font-size: 12px;
      }
    }
  `;

  document.head.appendChild(style);
  document.body.appendChild(button);

  // 添加点击事件
  button.addEventListener('click', async () => {
    try {
      button.style.opacity = '0.7';
      button.style.pointerEvents = 'none';

      const item = await collectCurrentItem();

      // 保存采集结果
      chrome.storage.local.set({ 'currentItem': [item] }, () => {
        console.log('当前商品数据已保存');
        showNotification(`采集完成！商品：${item.title}`, 'success');

        // 恢复按钮状态
        button.style.opacity = '1';
        button.style.pointerEvents = 'auto';
      });

    } catch (error) {
      console.error('采集失败:', error);
      showNotification('采集失败: ' + error.message, 'error');

      // 恢复按钮状态
      button.style.opacity = '1';
      button.style.pointerEvents = 'auto';
    }
  });
}

// 监听来自popup的消息
chrome.runtime.onMessage.addListener((request, _sender, sendResponse) => {
  try {
    switch (request.action) {
      case 'startCollection':
        // 这是从悬浮窗开始按钮触发的采集
        if (document.querySelector('.feeds-list-container--UkIMBPNk')) {
          // 在搜索页面，开始搜索采集
          startSearchCollection(0, 1); // 默认参数
        } else {
          showNotification('请在正确的页面使用此功能！', 'error');
        }
        break;
      case 'collectSearch':
        // 检查是否在正确的页面
        if (!document.querySelector('.feeds-list-container--UkIMBPNk')) {
          showNotification('请在XY搜索结果页面使用此功能！', 'error');
          sendResponse({ success: false, error: '页面类型错误' });
          return;
        }

        enableDetailCollect = request.enableDetailCollect || false;
        detailInterval = request.detailInterval || 3;
        startSearchCollection(request.minWantCount, request.pageCount);
        break;
      case 'exportSearch':
        exportSearchData();
        break;
      case 'stopCollection':
        shouldStop = true;
        isCollecting = false;
        isPaused = false;
        showNotification('采集已停止，可以导出当前数据', 'info');

        // 启用下载按钮，让用户可以导出当前已采集的数据
        const floatingWindow = document.getElementById('xy-floating-window');
        if (floatingWindow) {
          const downloadBtn = floatingWindow.querySelector('#xy-download');
          const stopBtn = floatingWindow.querySelector('#xy-stop');
          const startBtn = floatingWindow.querySelector('#xy-start');
          const pauseBtn = floatingWindow.querySelector('#xy-pause');

          if (downloadBtn) downloadBtn.disabled = false;
          if (stopBtn) stopBtn.disabled = true;
          if (startBtn) startBtn.disabled = false;
          if (pauseBtn) pauseBtn.disabled = true;
        }
        break;
      case 'pauseCollection':
        isPaused = true;
        showNotification('采集已暂停', 'info');

        // 更新按钮状态
        const pauseFloatingWindow = document.getElementById('xy-floating-window');
        if (pauseFloatingWindow) {
          const startBtn = pauseFloatingWindow.querySelector('#xy-start');
          const pauseBtn = pauseFloatingWindow.querySelector('#xy-pause');

          if (startBtn) startBtn.disabled = false;
          if (pauseBtn) pauseBtn.disabled = true;
        }
        break;
      case 'resumeCollection':
        isPaused = false;
        showNotification('采集已恢复', 'info');

        // 更新按钮状态
        const resumeFloatingWindow = document.getElementById('xy-floating-window');
        if (resumeFloatingWindow) {
          const startBtn = resumeFloatingWindow.querySelector('#xy-start');
          const pauseBtn = resumeFloatingWindow.querySelector('#xy-pause');

          if (startBtn) startBtn.disabled = true;
          if (pauseBtn) pauseBtn.disabled = false;
        }
        break;
      case 'collectShop':
        try {
          // 检查是否在店铺页面
          if (!document.querySelector('.feeds-list-container--UkIMBPNk')) {
            showNotification('请在XY店铺页面使用此功能！', 'error');
            sendResponse({ success: false, error: '页面类型错误' });
            return;
          }

          enableDetailCollect = request.enableDetailCollect || false;
          detailInterval = request.detailInterval || 3;

          // 开始采集店铺商品
          collectShopItems({
            maxItems: request.maxItems,
            minWantCount: request.minWantCount,
            enableDetailCollect: enableDetailCollect,
            detailInterval: detailInterval
          }).then(items => {
            // 保存采集结果
            chrome.storage.local.set({ 'shopItems': items }, () => {
              console.log('店铺商品数据已保存');
              // 不在这里显示通知，因为主函数中已经有了弹窗和悬浮窗提示
            });
          }).catch(error => {
            console.error('采集失败:', error);
            showNotification('采集失败: ' + error.message, 'error');
            // 也显示弹窗
            alert('采集失败: ' + error.message);
          });

          // 发送响应
          sendResponse({ success: true });
        } catch (error) {
          console.error('采集出错:', error);
          showNotification('采集出错: ' + error.message, 'error');
          sendResponse({ success: false, error: error.message });
        }
        break;
      case 'exportShop':
        exportShopData();
        break;
      case 'exportSearch':
        exportSearchData();
        break;
      case 'exportCurrentItem':
        exportCurrentItemData();
        break;
      case 'collectCurrentItem':
        console.log('📱 收到采集当前商品请求');
        try {
          // 检查是否在商品详情页
          console.log('🔍 开始检查页面类型...');
          if (!isItemDetailPage()) {
            console.log('❌ 页面检测失败，不是商品详情页');
            showNotification('请在商品详情页使用此功能！', 'error');
            sendResponse({ success: false, error: '页面类型错误' });
            return;
          }

          console.log('✅ 页面检测通过，开始采集商品数据');

          // 开始采集当前商品（异步执行，不阻塞响应）
          collectCurrentItem().then(item => {
            console.log('✅ 商品数据采集成功:', item);
            // 保存采集结果
            chrome.storage.local.set({ 'currentItem': [item] }, () => {
              console.log('当前商品数据已保存');
              showNotification(`采集完成！商品：${item.title}`, 'success');
            });
          }).catch(error => {
            console.error('❌ 采集失败:', error);
            showNotification('采集失败: ' + error.message, 'error');
          });

          // 立即发送响应，不等待采集完成
          sendResponse({ success: true });
        } catch (error) {
          console.error('❌ 采集出错:', error);
          showNotification('采集出错: ' + error.message, 'error');
          sendResponse({ success: false, error: error.message });
        }
        break;
      case 'showFloatingWindow':
        // 重新显示悬浮窗
        chrome.storage.local.remove('floatingWindowClosed', () => {
          createFloatingWindow();
          showNotification('悬浮窗已重新打开', 'info');
        });
        break;
    }
    sendResponse({ success: true });
  } catch (error) {
    console.error('消息处理错误：', error);
    showNotification('操作失败：' + error.message, 'error');
    sendResponse({ success: false, error: error.message });
  }
  return true; // 保持消息通道开启
});

// 显示通知
function showNotification(message, type = 'info') {
  // 移除已存在的通知
  const existingNotification = document.getElementById('xy-notification');
  if (existingNotification) {
    existingNotification.remove();
  }

  const notification = document.createElement('div');
  notification.id = 'xy-notification';
  notification.className = `xy - notification xy - notification - ${type} `;
  notification.innerHTML = `
            < div class= "xy-notification-content" >
      <span class="xy-notification-icon">${getNotificationIcon(type)}</span>
      <span class="xy-notification-message">${message}</span>
      <button class="xy-notification-close" onclick="this.parentElement.parentElement.remove()">×</button>
    </div >
              `;

  // 添加样式
  if (!document.getElementById('xy-notification-style')) {
    const style = document.createElement('style');
    style.id = 'xy-notification-style';
    style.textContent = `
                .xy - notification {
                  position: fixed;
                  top: 20px;
                  right: 20px;
                  z - index: 10000;
            min - width: 300px;
            max - width: 400px;
            border - radius: 8px;
            box - shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
            animation: slideInRight 0.3s ease;
          }
      
      .xy - notification - info {
            background: linear - gradient(135deg, #1890ff 0 %, #40a9ff 100 %);
            color: white;
          }
      
      .xy - notification - success {
            background: linear - gradient(135deg, #52c41a 0 %, #73d13d 100 %);
            color: white;
          }
      
      .xy - notification - error {
            background: linear - gradient(135deg, #ff4d4f 0 %, #ff7875 100 %);
            color: white;
          }
      
      .xy - notification - warning {
            background: linear - gradient(135deg, #fa8c16 0 %, #ffa940 100 %);
            color: white;
          }
      
      .xy - notification - content {
            padding: 16px;
            display: flex;
            align - items: center;
            gap: 12px;
          }
      
      .xy - notification - icon {
            font - size: 18px;
            flex - shrink: 0;
          }
      
      .xy - notification - message {
            flex: 1;
            font - size: 14px;
            line - height: 1.4;
          }
      
      .xy - notification - close {
            background: none;
            border: none;
            color: white;
            font - size: 18px;
            cursor: pointer;
            padding: 0;
            width: 24px;
            height: 24px;
            border - radius: 50 %;
            display: flex;
            align - items: center;
            justify - content: center;
            transition: background 0.2s;
          }
      
      .xy - notification - close:hover {
            background: rgba(255, 255, 255, 0.2);
          }

          @keyframes slideInRight {
        from {
              transform: translateX(100 %);
              opacity: 0;
            }
        to {
              transform: translateX(0);
              opacity: 1;
            }
          }
          `;
    document.head.appendChild(style);
  }

  document.body.appendChild(notification);

  // 自动移除通知
  setTimeout(() => {
    if (notification.parentElement) {
      notification.style.animation = 'slideInRight 0.3s ease reverse';
      setTimeout(() => notification.remove(), 300);
    }
  }, 5000);
}

// 获取通知图标
function getNotificationIcon(type) {
  const icons = {
    info: 'ℹ️',
    success: '✅',
    error: '❌',
    warning: '⚠️'
  };
  return icons[type] || icons.info;
}

// 检测是否在商品详情页
function isItemDetailPage() {
  // 检查商品详情页的特征元素
  const titleElement = document.querySelector('.item-title--DsJ54G6I');
  const priceElement = document.querySelector('.price--pzTeu3Ll');

  console.log('🔍 页面检测:', {
    titleElement: !!titleElement,
    priceElement: !!priceElement,
    titleText: titleElement ? titleElement.textContent.trim() : '未找到',
    priceText: priceElement ? priceElement.textContent.trim() : '未找到'
  });

  // 至少要有标题和价格元素才认为是商品详情页
  const isDetailPage = !!(titleElement && priceElement);
  console.log('📱 是否为商品详情页:', isDetailPage);

  return isDetailPage;
}

// 采集当前商品详情页的数据（直接复制fetchItemDetails的逻辑）
async function collectCurrentItem() {
  console.group('🔍 开始采集当前商品详情页');
  console.time('当前商品采集耗时');

  try {
    // 检查是否在商品详情页
    if (!isItemDetailPage()) {
      throw new Error('当前页面不是商品详情页');
    }

    console.log('✅ 确认在商品详情页，开始提取数据');

    // 直接从DOM结构提取关键数据（复制fetchItemDetails的逻辑）
    const itemDetail = {};

    // 1. 提取完整标题 - 尝试多种方法获取完整标题
    // 方法1: 标准DOM元素
    const titleElem = document.querySelector('.item-title--DsJ54G6I');

    // 方法2: 使用页面内标题
    const h1Elem = document.querySelector('h1.title') ||
      document.querySelector('h1') ||
      document.querySelector('.title');

    // 方法3: 获取页面Title (通常包含完整标题)
    const pageTitle = document.title;

    // 方法4: 检查meta标签中的标题
    const metaTitle = document.querySelector('meta[property="og:title"]')?.content ||
      document.querySelector('meta[name="title"]')?.content;

    // 尝试从多个来源获取最长、最完整的标题
    let titleCandidates = [];

    if (titleElem) {
      // 获取文本内容，也获取innerHTML，有时innerHTML包含更完整的内容
      titleCandidates.push({
        source: 'DOM元素',
        text: titleElem.textContent.trim(),
        length: titleElem.textContent.trim().length
      });

      // 有时需要获取元素的完整HTML内容以避免文本被CSS截断
      const fullHTML = titleElem.innerHTML;
      if (fullHTML && fullHTML.trim().length > 0) {
        // 移除HTML标签，保留纯文本
        const tempDiv = document.createElement('div');
        tempDiv.innerHTML = fullHTML;
        const htmlText = tempDiv.textContent.trim();
        titleCandidates.push({
          source: 'DOM元素HTML',
          text: htmlText,
          length: htmlText.length
        });
      }
    }

    if (h1Elem) {
      titleCandidates.push({
        source: 'H1标签',
        text: h1Elem.textContent.trim(),
        length: h1Elem.textContent.trim().length
      });
    }

    if (pageTitle) {
      // 通常页面标题包含网站名称，需要清理
      let cleanPageTitle = pageTitle.split(' - ')[0].trim();
      titleCandidates.push({
        source: '页面标题',
        text: cleanPageTitle,
        length: cleanPageTitle.length
      });
    }

    if (metaTitle) {
      titleCandidates.push({
        source: 'Meta标签',
        text: metaTitle,
        length: metaTitle.length
      });
    }

    // 按长度降序排序，优先选择最长的标题作为最完整的
    titleCandidates.sort((a, b) => b.length - a.length);

    // 选择最长的标题作为最终标题
    itemDetail.fullTitle = titleCandidates.length > 0 ? titleCandidates[0].text : '';
    console.log(`📌 选择的最终标题(${itemDetail.fullTitle.length}字符): ${itemDetail.fullTitle}`);

    // 2. 提取当前价格和原价
    const priceElem = document.querySelector('.price--pzTeu3Ll');
    itemDetail.currentPrice = priceElem ? priceElem.textContent.trim() : '';
    const originalPriceElem = document.querySelector('.original--Wijp49vq');
    itemDetail.originalPrice = originalPriceElem ? originalPriceElem.textContent.trim() : '';
    console.log(`💰 价格: ${itemDetail.currentPrice || '未找到'} ${itemDetail.originalPrice ? '(原价:' + itemDetail.originalPrice + ')' : ''}`);

    // 3. 提取商品描述
    let descFound = false;
    const descElem = document.querySelector('.item-desc--I9UWRak7');
    if (descElem) {
      itemDetail.description = descElem.textContent.trim();
      descFound = true;
      console.log(`📝 商品描述: ${itemDetail.description.length} 字符`);
    }

    if (!descFound) {
      itemDetail.description = '';
      console.log('⚠️ 未找到商品描述');
    }

    // 4. 提取统计数据（想要人数、浏览量等）
    let wantNum = 0;
    let viewNum = 0;

    const statsContainer = document.querySelector('.want--ecByv3Sr');
    if (statsContainer) {
      console.log('✅ 找到统计数据容器: ', statsContainer.textContent);

      // 解析想要人数
      const wantMatch = statsContainer.textContent.match(/想要[：:\s]*(\d+)/);
      if (wantMatch) {
        wantNum = parseInt(wantMatch[1]);
        console.log(`📊 解析到想要人数: ${wantNum}`);
      }

      // 解析浏览量
      const viewMatch = statsContainer.textContent.match(/浏览[：:\s]*(\d+)/);
      if (viewMatch) {
        viewNum = parseInt(viewMatch[1]);
        console.log(`📊 解析到浏览量: ${viewNum}`);
      }
    }

    itemDetail.wantCount = wantNum || 0;
    itemDetail.viewCount = viewNum || 0;
    itemDetail.favorCount = 0;

    console.log(`📊 最终统计数据: 浏览(${itemDetail.viewCount}) 想要(${itemDetail.wantCount}) 收藏(${itemDetail.favorCount || 0})`);

    // 5. 提取所有图片链接
    let imgUrls = new Set();

    const imgContainer = document.querySelector('.item-main-window-list--od7DK4Fm');
    if (imgContainer) {
      const imgs = imgContainer.querySelectorAll('img');
      console.log(`🖼️ 从指定容器找到 ${imgs.length} 张图片`);
      Array.from(imgs).forEach(img => {
        if (img.src) {
          const cleanUrl = img.src
            .replace(/^\/\//, 'https://')
            .replace(/\_\d+x\d+.*$/, '')
            .replace(/\_Q90.*$/, '')
            .replace(/\.webp$/, '.jpg');
          imgUrls.add(cleanUrl);
          console.log(`📸 添加图片: ${cleanUrl}`);
        }
      });
    }

    itemDetail.images = Array.from(imgUrls);
    console.log(`🖼️ 最终图片集合: 找到${itemDetail.images.length} 张`);

    // 6. 从当前URL提取商品ID和链接
    const currentUrl = window.location.href;
    itemDetail.link = currentUrl;

    // 尝试从URL提取商品ID
    const idMatches = [
      currentUrl.match(/id=(\d+)/),
      currentUrl.match(/\/item\/(\d+)/),
      currentUrl.match(/\/(\d+)$/),
      currentUrl.match(/itemId=(\d+)/),
    ];

    for (const match of idMatches) {
      if (match && match[1]) {
        itemDetail.itemId = match[1];
        console.log(`🔍 提取到商品ID: ${itemDetail.itemId}`);
        break;
      }
    }

    // 7. 设置基本信息
    itemDetail.title = itemDetail.fullTitle || '未知商品';
    itemDetail.hasDetailData = true;
    itemDetail.collectTime = new Date().toLocaleString();

    console.log('✅ 当前商品数据采集完成:', itemDetail);
    console.timeEnd('当前商品采集耗时');
    console.groupEnd();

    return itemDetail;

  } catch (error) {
    console.error('❌ 当前商品数据提取错误:', error);
    console.timeEnd('当前商品采集耗时');
    console.groupEnd();
    throw error;
  }
}

// 详细采集商品详情页的数据
async function fetchItemDetails(url) {
  console.group(`🔍 开始采集详情页: ${url} `);
  console.time('详情页采集耗时');

  return new Promise(async (resolve, reject) => {
    try {
      // 创建一个新的标签页打开商品详情
      console.log('📂 正在打开新标签页...');
      const newTab = window.open(url, '_blank');

      if (!newTab) {
        console.error('❌ 无法打开新标签页，可能被浏览器阻止了弹出窗口');
        console.timeEnd('详情页采集耗时');
        console.groupEnd();
        throw new Error('无法打开新标签页，请允许弹出窗口');
      }

      // 设置超时处理
      const timeout = setTimeout(() => {
        console.error('❌ 详情页采集超时 (30秒)');
        console.timeEnd('详情页采集耗时');
        console.groupEnd();
        try { newTab.close(); } catch (e) { }
        reject(new Error('详情页采集超时'));
      }, 30000); // 30秒超时

      // 轮询等待页面加载完成并采集数据
      console.log('⌛ 等待页面加载...');
      let checkCount = 0;
      const checkInterval = setInterval(() => {
        try {
          if (newTab.closed) {
            clearInterval(checkInterval);
            clearTimeout(timeout);
            console.error('❌ 详情页被意外关闭');
            console.timeEnd('详情页采集耗时');
            console.groupEnd();
            reject(new Error('详情页被意外关闭'));
            return;
          }

          if (newTab.document.readyState !== 'complete') {
            checkCount++;
            if (checkCount % 5 === 0) {  // 每5次检查输出一次日志
              console.log(`⌛ 页面加载中... (${checkCount}次检查)`);
            }
            return; // 页面仍在加载
          }

          console.log('✅ 详情页加载完成，开始提取数据');

          // 尝试获取JS全局变量中的商品详情数据
          let detailData = {};

          try {
            // 尝试执行脚本获取详细数据
            console.log('🔍 尝试从JS全局变量获取数据...');
            const extractDataScript = `
          (function () {
            try {
              // 调试输出所有可能包含数据的全局变量
              console.log("DEBUG - 全局变量检查:",
                "window.__INITIAL_DATA__:", !!window.__INITIAL_DATA__,
                "window.__NEXT_DATA__:", !!window.__NEXT_DATA__,
                "window.g_config:", !!window.g_config
              );

              if (window.__INITIAL_DATA__ && window.__INITIAL_DATA__.data) {
                return JSON.stringify(window.__INITIAL_DATA__.data);
              }

              // 尝试其他可能的数据来源
              if (window.__NEXT_DATA__ && window.__NEXT_DATA__.props) {
                return JSON.stringify(window.__NEXT_DATA__.props);
              }

              return null;
            } catch (err) {
              console.error("数据提取错误:", err);
              return null;
            }
          })()
            `;

            const extractedData = newTab.eval(extractDataScript);
            if (extractedData) {
              detailData = JSON.parse(extractedData);
              console.log('📊 从JS变量中成功提取数据', detailData);
            } else {
              console.log('⚠️ JS变量中未找到有效数据');
            }
          } catch (err) {
            console.warn('⚠️ JS变量提取失败，将从页面DOM中提取', err);
          }

          console.log('🔍 从DOM结构提取关键数据...');
          // 从页面DOM中提取关键数据
          const itemDetail = {};

          // 1. 提取完整标题 - 尝试多种方法获取完整标题
          // 方法1: 标准DOM元素
          const titleElement = newTab.document.querySelector('.item-title--DsJ54G6I');

          // 方法2: 使用页面内标题
          const h1Element = newTab.document.querySelector('h1.title') ||
            newTab.document.querySelector('h1') ||
            newTab.document.querySelector('.title');

          // 方法3: 获取页面Title (通常包含完整标题)
          const pageTitle = newTab.document.title;

          // 方法4: 检查meta标签中的标题
          const metaTitle = newTab.document.querySelector('meta[property="og:title"]')?.content ||
            newTab.document.querySelector('meta[name="title"]')?.content;

          // 尝试从多个来源获取最长、最完整的标题
          let possibleTitles = [];

          if (titleElement) {
            // 获取文本内容，也获取innerHTML，有时innerHTML包含更完整的内容
            possibleTitles.push({
              source: 'DOM元素',
              text: titleElement.textContent.trim(),
              length: titleElement.textContent.trim().length
            });

            // 有时需要获取元素的完整HTML内容以避免文本被CSS截断
            const fullHTML = titleElement.innerHTML;
            if (fullHTML && fullHTML.trim().length > 0) {
              // 移除HTML标签，保留纯文本
              const tempDiv = newTab.document.createElement('div');
              tempDiv.innerHTML = fullHTML;
              const htmlText = tempDiv.textContent.trim();
              possibleTitles.push({
                source: 'DOM元素HTML',
                text: htmlText,
                length: htmlText.length
              });
            }
          }

          if (h1Element) {
            possibleTitles.push({
              source: 'H1标签',
              text: h1Element.textContent.trim(),
              length: h1Element.textContent.trim().length
            });
          }

          if (pageTitle) {
            // 通常页面标题包含网站名称，需要清理
            let cleanPageTitle = pageTitle.split(' - ')[0].trim();
            possibleTitles.push({
              source: '页面标题',
              text: cleanPageTitle,
              length: cleanPageTitle.length
            });
          }

          if (metaTitle) {
            possibleTitles.push({
              source: 'Meta标签',
              text: metaTitle,
              length: metaTitle.length
            });
          }

          // 获取API数据中的标题
          if (detailData && detailData.itemDO && detailData.itemDO.title) {
            possibleTitles.push({
              source: 'API数据',
              text: detailData.itemDO.title,
              length: detailData.itemDO.title.length
            });
          } else if (detailData && detailData.pageProps && detailData.pageProps.data && detailData.pageProps.data.title) {
            possibleTitles.push({
              source: 'API数据(pageProps)',
              text: detailData.pageProps.data.title,
              length: detailData.pageProps.data.title.length
            });
          }

          // 按长度降序排序，优先选择最长的标题作为最完整的
          possibleTitles.sort((a, b) => b.length - a.length);

          // 调试输出所有候选标题
          console.log('📌 候选标题列表:');
          possibleTitles.forEach((candidate, index) => {
            console.log(`候选 ${index + 1} [${candidate.source}](${candidate.length}字符): ${candidate.text} `);
          });

          // 选择最长的标题作为最终标题
          itemDetail.fullTitle = possibleTitles.length > 0 ? possibleTitles[0].text : '';

          // 调试输出选择的标题
          console.log(`📌 选择的最终标题(${itemDetail.fullTitle.length}字符): ${itemDetail.fullTitle} `);

          // 2. 提取当前价格和原价
          const priceElement = newTab.document.querySelector('.price--pzTeu3Ll');
          itemDetail.currentPrice = priceElement ? priceElement.textContent.trim() : '';
          const originalPriceEl = newTab.document.querySelector('.original--Wijp49vq');
          itemDetail.originalPrice = originalPriceEl ? originalPriceEl.textContent.trim() : '';
          console.log(`💰 价格: ${itemDetail.currentPrice || '未找到'} ${itemDetail.originalPrice ? '(原价:' + itemDetail.originalPrice + ')' : ''} `);

          // 3. 提取商品描述 - 尝试多个选择器
          let descriptionFound = false;

          // 尝试方法1：标准选择器
          const descElement = newTab.document.querySelector('.item-desc--I9UWRak7');
          if (descElement) {
            itemDetail.description = descElement.textContent.trim();
            descriptionFound = true;
            console.log(`📝 商品描述(标准): ${itemDetail.description.length} 字符`);
          }

          // 尝试方法2：查找带有desc类名的元素
          if (!descriptionFound) {
            const descElements = newTab.document.querySelectorAll('[class*="desc"],[class*="main--"]');
            for (const elem of descElements) {
              if (elem.textContent && elem.textContent.length > 30) {
                itemDetail.description = elem.textContent.trim();
                descriptionFound = true;
                console.log(`📝 商品描述(desc类): ${itemDetail.description.length} 字符`);
                break;
              }
            }
          }

          // 尝试方法3：从页面实际结构中查找描述
          if (!descriptionFound) {
            // 查找注意到的特殊结构元素
            const detailDesc = newTab.document.querySelector('.desc--GaIUKUQY');
            if (detailDesc) {
              itemDetail.description = detailDesc.textContent.trim();
              descriptionFound = true;
              console.log(`📝 商品描述(特定结构): ${itemDetail.description.length} 字符`);
            }
          }

          // 尝试方法4：查找长文本内容可能是描述的元素
          if (!descriptionFound) {
            const allTextElements = newTab.document.querySelectorAll('p, div, span');
            let longestText = '';

            for (const elem of allTextElements) {
              const text = elem.textContent.trim();
              // 排除明显不是描述的元素
              if (text && text.length > 50 &&
                !text.includes('function') &&
                !text.includes('return') &&
                !text.includes('var ')) {
                if (text.length > longestText.length) {
                  longestText = text;
                }
              }
            }

            if (longestText) {
              itemDetail.description = longestText;
              descriptionFound = true;
              console.log(`📝 商品描述(最长文本): ${itemDetail.description.length} 字符`);
            }
          }

          // 找不到描述时的默认处理
          if (!descriptionFound) {
            itemDetail.description = '';
            console.log('⚠️ 未找到商品描述');
          } else {
            // 清理描述中的特殊标记
            itemDetail.description = itemDetail.description
              .replace(/\[hot\]|\[new\]|\[cool\]|\[VS\]|\[红旗\]|\[红圆\]|\[黄圆\]|\[闪亮\]/g, '')
              .replace(/\s+/g, ' ')
              .trim();
          }

          // 4. 尝试多种选择器获取统计数据（想要人数、浏览量等）
          console.log('🔍 尝试获取统计数据...');

          // 初始化统计变量
          let wantCountAlt = 0;
          let viewCountAlt = 0;
          let wantCountEl = null;
          let viewCountEl = null;

          // 直接解析带有想要和浏览信息的容器
          const wantContainer = newTab.document.querySelector('.want--ecByv3Sr');
          if (wantContainer) {
            console.log('✅ 找到统计数据容器: ', wantContainer.textContent);

            // 遍历子元素，查找想要人数和浏览量
            const childElements = Array.from(wantContainer.children);
            for (const element of childElements) {
              const text = element.textContent.trim();
              if (text.includes('人想要')) {
                wantCountAlt = parseInt(text.replace(/[^\d]/g, '')) || 0;
                console.log(`找到想要人数: ${wantCountAlt} `);
              } else if (text.includes('浏览')) {
                // 处理万次浏览的情况
                let extracted = 0;
                if (text.includes('万')) {
                  // 提取万前面的数字，然后乘以10000
                  const match = text.match(/(\d+(?:\.\d+)?)万/);
                  if (match) {
                    extracted = Math.floor(parseFloat(match[1]) * 10000);
                    console.log(`检测到万次浏览: ${match[1]} 万 -> ${extracted} `);
                  }
                } else {
                  // 普通数字处理
                  extracted = parseInt(text.replace(/[^\d]/g, '')) || 0;
                }

                if (extracted > 0 && (!viewCountAlt || extracted > viewCountAlt)) {
                  viewCountAlt = extracted;
                  console.log(`文本匹配找到浏览量: ${viewCountAlt} `);
                }
              }
            }
          }

          // 备用方法：使用通配符选择器查找所有可能的统计元素
          const allStatElements = newTab.document.querySelectorAll('[class*="want"],[class*="stat"],[class*="browse"],[class*="view"]');
          console.log(`找到 ${allStatElements.length} 个可能的统计元素`);

          allStatElements.forEach(el => {
            const text = el.textContent;
            if (text) {
              if (text.includes('人想要') || text.includes('想要')) {
                const extracted = parseInt(text.replace(/[^\d]/g, '')) || 0;
                if (extracted > 0) {
                  console.log(`通配符匹配找到想要人数: ${extracted} (元素: ${el.className})`);
                  if (!wantCountAlt || extracted > wantCountAlt) {
                    wantCountAlt = extracted;
                  }
                }
              } else if (text.includes('浏览')) {
                // 处理万次浏览的情况
                let extracted = 0;
                if (text.includes('万')) {
                  // 提取万前面的数字，然后乘以10000
                  const match = text.match(/(\d+(?:\.\d+)?)万/);
                  if (match) {
                    extracted = Math.floor(parseFloat(match[1]) * 10000);
                    console.log(`检测到万次浏览: ${match[1]} 万 -> ${extracted} `);
                  }
                } else {
                  // 普通数字处理
                  extracted = parseInt(text.replace(/[^\d]/g, '')) || 0;
                }

                if (extracted > 0 && (!viewCountAlt || extracted > viewCountAlt)) {
                  viewCountAlt = extracted;
                  console.log(`文本匹配找到浏览量: ${viewCountAlt} `);
                }
              }
            }
          });

          // 方法3: 文本匹配查找所有包含关键字的元素
          const allElements = Array.from(newTab.document.querySelectorAll('*'));
          const statsElements = allElements.filter(el => {
            const text = el.textContent;
            return text && (
              (text.includes('人想要') || text.includes('浏览')) &&
              /\d+/.test(text) && // 包含数字
              text.length < 20 // 排除长文本
            );
          });

          console.log(`通过文本匹配找到 ${statsElements.length} 个可能的统计元素`);
          statsElements.forEach(el => {
            const text = el.textContent.trim();
            console.log(`潜在统计信息: "${text}"(${el.className})`);

            if (text.includes('人想要') || text.includes('想要')) {
              const extracted = parseInt(text.replace(/[^\d]/g, '')) || 0;
              if (extracted > 0 && (!wantCountAlt || extracted > wantCountAlt)) {
                wantCountAlt = extracted;
                console.log(`文本匹配找到想要人数: ${wantCountAlt} `);
              }
            } else if (text.includes('浏览')) {
              // 处理万次浏览的情况
              let extracted = 0;
              if (text.includes('万')) {
                // 提取万前面的数字，然后乘以10000
                const match = text.match(/(\d+(?:\.\d+)?)万/);
                if (match) {
                  extracted = Math.floor(parseFloat(match[1]) * 10000);
                  console.log(`检测到万次浏览: ${match[1]} 万 -> ${extracted} `);
                }
              } else {
                // 普通数字处理
                extracted = parseInt(text.replace(/[^\d]/g, '')) || 0;
              }

              if (extracted > 0 && (!viewCountAlt || extracted > viewCountAlt)) {
                viewCountAlt = extracted;
                console.log(`文本匹配找到浏览量: ${viewCountAlt} `);
              }
            }
          });

          // 采用最终结果
          itemDetail.wantCount = wantCountAlt ||
            (wantCountEl ? parseInt(wantCountEl.textContent.replace(/[^\d]/g, '')) : 0);

          itemDetail.viewCount = viewCountAlt ||
            (viewCountEl ? parseInt(viewCountEl.textContent.replace(/[^\d]/g, '')) : 0);

          console.log(`📊 最终统计数据: 浏览(${itemDetail.viewCount}) 想要(${itemDetail.wantCount}) 收藏(${itemDetail.favorCount || 0})`);

          // 5. 提取商品分类
          console.log('🔍 尝试查找分类信息...');
          const categoryAttempts = [
            newTab.document.querySelector('.item-info-wrap--RzpuxL0M .item-info-row--KvYXp4zy:nth-child(1) .info-tag--cjaQNRzA'),
            ...Array.from(newTab.document.querySelectorAll('*[class*="info-tag"]')),
            ...Array.from(newTab.document.querySelectorAll('*[class*="category"]'))
          ];

          let categoryFound = false;
          for (const elem of categoryAttempts) {
            if (elem && elem.textContent) {
              const text = elem.textContent.trim();
              console.log(`可能的分类: "${text}"`);
              if (!categoryFound && text.length > 0 && text.length < 20) {
                itemDetail.category = text;
                categoryFound = true;
                break;
              }
            }
          }
          console.log(`🏷️ 分类: ${itemDetail.category || '未找到'} `);

          // 6. 提取发布时间
          console.log('🔍 尝试查找发布时间...');
          const timeAttempts = [
            newTab.document.querySelector('.item-info-wrap--RzpuxL0M .item-info-row--KvYXp4zy:nth-child(2) .info-tag--cjaQNRzA'),
            ...Array.from(newTab.document.querySelectorAll('*[class*="time"]')),
            ...Array.from(newTab.document.querySelectorAll('*[class*="date"]')),
            ...Array.from(newTab.document.querySelectorAll('*[class*="publish"]'))
          ];

          let timeFound = false;
          for (const elem of timeAttempts) {
            if (elem && elem.textContent) {
              const text = elem.textContent.trim();
              console.log(`可能的时间: "${text}"`);
              if (!timeFound && text.includes('20') || text.includes('分钟前') || text.includes('小时前') || text.includes('天前')) {
                itemDetail.publishTime = text;
                timeFound = true;
                break;
              }
            }
          }
          console.log(`⏰ 发布时间: ${itemDetail.publishTime || '未找到'} `);

          // 7. 提取所有图片链接 - 优先从指定容器采集
          let imageUrls = new Set(); // 使用Set去重

          // 方法1: 优先从指定的商品图片容器中采集
          const mainImageContainer = newTab.document.querySelector('.item-main-window-list--od7DK4Fm');
          if (mainImageContainer) {
            const containerImages = mainImageContainer.querySelectorAll('img');
            console.log(`🖼️ 从指定容器找到 ${containerImages.length} 张图片`);
            Array.from(containerImages).forEach(img => {
              if (img.src) {
                const cleanUrl = img.src
                  .replace(/^\/\//, 'https://')
                  .replace(/\_\d+x\d+.*$/, '')
                  .replace(/\_Q90.*$/, '')
                  .replace(/\.webp$/, '.jpg');
                imageUrls.add(cleanUrl);
                console.log(`📸 添加图片: ${cleanUrl} `);
              }
            });
          } else {
            console.log('⚠️ 未找到指定的图片容器，尝试备用方法');
          }

          // 方法2: 备用方法 - 标准商品图片选择器（仅在主容器未找到时使用）
          if (imageUrls.size === 0) {
            const imageElems = newTab.document.querySelectorAll('.item-main-window-list-item--gXUlMEkj img');
            if (imageElems && imageElems.length > 0) {
              console.log(`🖼️ 从标准选择器找到 ${imageElems.length} 张图片`);
              Array.from(imageElems).forEach(img => {
                if (img.src) {
                  const cleanUrl = img.src
                    .replace(/^\/\//, 'https://')
                    .replace(/\_\d+x\d+.*$/, '')
                    .replace(/\.webp$/, '.jpg');
                  imageUrls.add(cleanUrl);
                }
              });
            }
          }

          // 方法3: 最后备用方法 - 查找轮播图（仅在前面方法都失败时使用）
          if (imageUrls.size === 0) {
            const carouselImages = newTab.document.querySelectorAll('.carousel--MBcZaegk img, .carouselItem--jwFj0Jpa img, .slick-slide img');
            if (carouselImages && carouselImages.length > 0) {
              console.log(`🖼️ 从轮播图找到 ${carouselImages.length} 张图片`);
              Array.from(carouselImages).forEach(img => {
                if (img.src) {
                  const cleanUrl = img.src
                    .replace(/^\/\//, 'https://')
                    .replace(/\_\d+x\d+.*$/, '')
                    .replace(/\_Q90.*$/, '')
                    .replace(/\.webp$/, '.jpg');
                  imageUrls.add(cleanUrl);
                }
              });
            }
          }

          // 转换为数组
          itemDetail.images = Array.from(imageUrls);
          console.log(`🖼️ 最终图片集合: 找到${itemDetail.images.length} 张`);
          itemDetail.images.forEach((url, i) => console.log(`图片${i + 1}: ${url} `));

          // 如果成功从JS变量获取了数据，合并这些数据
          if (detailData && (detailData.itemDO || detailData.pageProps && detailData.pageProps.data)) {
            console.log('🔄 合并API数据和DOM数据...');
            // 适配不同的数据结构
            const apiData = detailData.itemDO || (detailData.pageProps && detailData.pageProps.data);

            if (apiData) {
              console.log('📊 API数据结构:', Object.keys(apiData));

              // 从API合并更多详细数据
              if (apiData.title && (!itemDetail.fullTitle || apiData.title.length > itemDetail.fullTitle.length)) {
                itemDetail.fullTitle = apiData.title;
                console.log(`📌 从API获取更完整标题: ${itemDetail.fullTitle} `);
              }
              if (apiData.desc) itemDetail.description = apiData.desc;
              if (apiData.itemId) itemDetail.itemId = apiData.itemId;

              // 合并统计数据
              if (apiData.wantCnt !== undefined) {
                itemDetail.wantCount = apiData.wantCnt;
                console.log(`📊 从API获取想要人数: ${itemDetail.wantCount} `);
              }

              if (apiData.browseCnt !== undefined) {
                itemDetail.viewCount = apiData.browseCnt;
                console.log(`📊 从API获取浏览数: ${itemDetail.viewCount} `);
              }

              if (apiData.favCnt !== undefined) {
                itemDetail.favorCount = apiData.favCnt;
                console.log(`📊 从API获取收藏数: ${itemDetail.favorCount} `);
              }

              // 提取分类信息
              if (apiData.itemLabelExtList && apiData.itemLabelExtList.length > 0) {
                console.log('📑 从API获取分类数据:', apiData.itemLabelExtList);
                const categoryLabel = apiData.itemLabelExtList.find(label => label.labelType === 'common');
                if (categoryLabel) {
                  itemDetail.category = categoryLabel.text || itemDetail.category;
                  console.log(`🏷️ 从API获取分类: ${itemDetail.category} `);
                }
              }

              // 提取图片信息
              if (apiData.imageInfos && apiData.imageInfos.length > 0) {
                itemDetail.images = apiData.imageInfos.map(img => img.url);
                console.log(`🖼️ 从API获取图片: ${itemDetail.images.length} 张`);
              }

              // 提取上架时间 - 优先从GMT_CREATE_DATE_KEY字段，其次proPolishTime
              if (apiData.GMT_CREATE_DATE_KEY) {
                try {
                  const publishDate = new Date(apiData.GMT_CREATE_DATE_KEY);
                  itemDetail.publishTime = publishDate.toLocaleString('zh-CN', {
                    year: 'numeric',
                    month: '2-digit',
                    day: '2-digit',
                    hour: '2-digit',
                    minute: '2-digit',
                    second: '2-digit'
                  });
                  console.log(`⏰ 从GMT_CREATE_DATE_KEY获取上架时间: ${itemDetail.publishTime} `);
                } catch (error) {
                  console.warn('⚠️ GMT_CREATE_DATE_KEY时间转换失败:', error);
                  itemDetail.publishTime = '时间格式错误';
                }
              } else if (apiData.proPolishTime) {
                try {
                  // proPolishTime是毫秒级时间戳，转换为可读日期
                  const publishDate = new Date(apiData.proPolishTime);
                  itemDetail.publishTime = publishDate.toLocaleString('zh-CN', {
                    year: 'numeric',
                    month: '2-digit',
                    day: '2-digit',
                    hour: '2-digit',
                    minute: '2-digit',
                    second: '2-digit'
                  });
                  console.log(`⏰ 从proPolishTime获取上架时间: ${itemDetail.publishTime} (原始时间戳: ${apiData.proPolishTime})`);
                } catch (error) {
                  console.warn('⚠️ proPolishTime时间转换失败:', error);
                  itemDetail.publishTime = '时间格式错误';
                }
              }
            }
          }

          // 清除定时器并关闭标签页
          clearInterval(checkInterval);
          clearTimeout(timeout);

          console.log('✅ 详情数据采集成功，准备关闭标签页');

          // 延迟关闭标签页
          setTimeout(() => {
            try {
              newTab.close();
              console.log('✅ 标签页已关闭');
            } catch (e) {
              console.warn('⚠️ 关闭标签页失败', e);
            }
            console.timeEnd('详情页采集耗时');
            console.groupEnd();
            resolve(itemDetail);
          }, 500);
        } catch (error) {
          console.error('❌ 详情页数据提取错误:', error);
          clearInterval(checkInterval);
          clearTimeout(timeout);
          try { newTab.close(); } catch (e) { }
          console.timeEnd('详情页采集耗时');
          console.groupEnd();
          reject(error);
        }
      }, 1000); // 每秒检查一次
    } catch (error) {
      console.error('❌ 打开详情页失败:', error);
      console.timeEnd('详情页采集耗时');
      console.groupEnd();
      reject(error);
    }
  });
}

// 开始搜索页面采集
async function startSearchCollection(minWantCount = 0, pageCount = 1) {
  if (!document.querySelector('.feeds-list-container--UkIMBPNk')) {
    console.error('❌ 未检测到搜索结果列表');
    alert('请在XY搜索结果页面使用此功能！');
    return;
  }

  if (isCollecting) {
    console.warn('⚠️ 正在采集中，请等待当前采集完成...');
    alert('正在采集中，请等待当前采集完成...');
    return;
  }

  try {
    isCollecting = true;
    shouldStop = false;
    isPaused = false;
    searchData = [];
    let currentPage = 1;
    let lastPageItemCount = 0; // 记录上一页的总数

    console.group('🔍 搜索商品采集任务');
    console.time('搜索采集总耗时');
    console.log('🚀 开始采集任务...');
    console.log(`📊 采集配置：最少想要人数 ${minWantCount}，计划采集 ${pageCount} 页，详细采集: ${enableDetailCollect ? '开启' : '关闭'}, 间隔: ${detailInterval} 秒`);

    // 获取实际可用的总页数
    const maxAvailablePages = getTotalPages();
    const targetPages = Math.min(pageCount, maxAvailablePages);
    console.log(`📑 检测到总页数：${maxAvailablePages}，实际将采集：${targetPages} 页`);

    // 先创建悬浮窗
    createFloatingWindow();

    // 然后设置按钮状态 - 采集开始时启用暂停和停止按钮
    const startFloatingWindow = document.getElementById('xy-floating-window');
    if (startFloatingWindow) {
      const startBtn = startFloatingWindow.querySelector('#xy-start');
      const pauseBtn = startFloatingWindow.querySelector('#xy-pause');
      const stopBtn = startFloatingWindow.querySelector('#xy-stop');
      const downloadBtn = startFloatingWindow.querySelector('#xy-download');

      if (startBtn) startBtn.disabled = true;
      if (pauseBtn) pauseBtn.disabled = false; // 启用暂停按钮
      if (stopBtn) stopBtn.disabled = false;
      if (downloadBtn) downloadBtn.disabled = true;

      console.log('🔘 按钮状态已更新：暂停和停止按钮已启用');
    }
    updateFloatingWindow({
      progress: 0,
      currentPage: 1,
      totalItems: 0,
      currentPageItems: 0,
      log: `开始采集...${enableDetailCollect ? '(启用详细采集)' : ''} `
    });

    while (currentPage <= targetPages && !shouldStop) {
      // 检查暂停状态
      while (isPaused && !shouldStop) {
        console.log('⏸️ 采集已暂停，等待恢复...');
        updateFloatingWindow({
          log: '采集已暂停，点击开始按钮恢复采集'
        });
        await new Promise(resolve => setTimeout(resolve, 1000));
      }

      if (shouldStop) break;

      console.group(`🔄 正在采集第 ${currentPage}/${targetPages} 页`);
      console.time(`第${currentPage}页采集耗时`);

      // 检查页面是否有效
      if (!document.querySelector('.feeds-item-wrap--rGdH_KoF')) {
        console.error('❌ 页面加载异常，未找到商品元素');
        throw new Error('页面加载异常，请刷新后重试');
      }

      // 记录当前总数
      lastPageItemCount = searchData.length;

      // 采集当前页面数据
      console.log('📥 开始采集当前页基础数据...');
      await collectCurrentPageData(minWantCount);

      // 检查是否暂停或停止
      if (shouldStop) break;
      while (isPaused && !shouldStop) {
        console.log('⏸️ 采集已暂停，等待恢复...');
        updateFloatingWindow({
          log: '采集已暂停，点击开始按钮恢复采集'
        });
        await new Promise(resolve => setTimeout(resolve, 1000));
      }
      if (shouldStop) break;

      const pageCollected = searchData.length - lastPageItemCount;
      console.log(`✅ 当前页成功采集 ${pageCollected} 条基础数据`);

      // 计算实际进度
      const progress = Math.floor((currentPage / targetPages) * 100);

      // 更新悬浮窗状态
      updateFloatingWindow({
        progress,
        currentPage,
        totalItems: searchData.length,
        currentPageItems: pageCollected,
        log: `第 ${currentPage} 页基础数据采集完成，获取 ${pageCollected} 条数据`
      });

      // 如果启用详细采集，为当前页新采集的商品获取详细信息
      if (enableDetailCollect && pageCollected > 0 && !shouldStop) {
        const startIndex = searchData.length - pageCollected;

        console.log(`🔍 开始获取第 ${currentPage} 页商品详细数据...`);
        console.time(`第${currentPage}页详细数据采集耗时`);

        updateFloatingWindow({
          log: `开始获取第 ${currentPage} 页商品详细数据...`
        });

        let detailSuccessCount = 0;
        let detailErrorCount = 0;

        for (let i = startIndex; i < searchData.length && !shouldStop; i++) {
          // 检查暂停状态
          while (isPaused && !shouldStop) {
            console.log('⏸️ 详细采集已暂停，等待恢复...');
            updateFloatingWindow({
              log: '详细采集已暂停，点击开始按钮恢复采集'
            });
            await new Promise(resolve => setTimeout(resolve, 1000));
          }

          if (shouldStop) break;

          const item = searchData[i];
          if (item.link) {
            try {
              console.log(`🔄 处理商品 ${i - startIndex + 1}/${pageCollected}: ${item.title}`);

              // 更新进度条，使用详情采集的子进度
              const overallProgress = Math.floor(((currentPage - 1 + (i - startIndex + 1) / pageCollected) / targetPages) * 100);

              updateFloatingWindow({
                progress: overallProgress,
                log: `正在获取详细数据 (${i - startIndex + 1}/${pageCollected}): ${item.title}`,
                currentPageItems: i - startIndex + 1
              });

              const detailData = await fetchItemDetails(item.link);

              // 保存原始的想要人数（从基础采集中获取的正确值）
              const originalWantCount = item.wantCount;

              // 合并详细数据到原始数据，但保留原始想要人数
              searchData[i] = {
                ...item,
                ...detailData,
                wantCount: originalWantCount, // 保留原始的正确想要人数
                hasDetailData: true
              };

              detailSuccessCount++;

              // 每个详情页采集后等待指定的间隔
              if (i < searchData.length - 1) {
                console.log(`⏱️ 等待 ${detailInterval} 秒后继续下一个...`);
                await new Promise(resolve => setTimeout(resolve, detailInterval * 1000));
              }

            } catch (error) {
              console.warn(`⚠️ 获取商品详情失败: ${item.title}`, error);
              searchData[i].detailError = error.message;
              detailErrorCount++;
            }
          }
        }

        console.log(`📊 详细数据采集统计：成功 ${detailSuccessCount}，失败 ${detailErrorCount}`);
        console.timeEnd(`第${currentPage}页详细数据采集耗时`);

        updateFloatingWindow({
          progress: Math.floor((currentPage / targetPages) * 100),
          log: `第 ${currentPage} 页详细数据采集完成 (成功${detailSuccessCount}/失败${detailErrorCount})`
        });
      }

      // 保存当前数据到storage
      await saveToStorage();
      console.log('💾 数据已保存到本地存储');

      // 如果已经达到目标页数，结束采集
      if (currentPage >= targetPages) {
        // 最后一页采集完成，更新为100%
        updateFloatingWindow({
          progress: 100,
          currentPage,
          totalItems: searchData.length,
          currentPageItems: pageCollected,
          log: '采集完成！'
        });
        console.log('✅ 已达到目标页数，采集完成');
        break;
      }

      // 尝试翻到下一页
      console.log('📄 准备翻到下一页...');
      let flipSuccess = false;
      let flipRetryCount = 0;
      const maxFlipRetries = 3; // 最多重试3次翻页

      while (!flipSuccess && flipRetryCount < maxFlipRetries && !shouldStop) {
        flipRetryCount++;
        console.log(`🔄 第${flipRetryCount}次尝试翻页到第${currentPage + 1}页...`);

        updateFloatingWindow({
          log: `第${flipRetryCount}次尝试翻页到第${currentPage + 1}页...`
        });

        const success = await goToNextPage();
        if (success) {
          flipSuccess = true;
          console.log(`✅ 第${flipRetryCount}次尝试成功翻到第${currentPage + 1}页`);
        } else {
          console.warn(`⚠️ 第${flipRetryCount}次翻页尝试失败`);

          if (flipRetryCount < maxFlipRetries) {
            console.log(`⏳ 等待3秒后进行第${flipRetryCount + 1}次重试...`);
            updateFloatingWindow({
              log: `翻页失败，等待3秒后重试...`
            });
            await new Promise(resolve => setTimeout(resolve, 3000));

            // 刷新页面状态，重新检测页码
            try {
              await waitForPageStable();
              const currentPageCheck = getCurrentPage();
              console.log(`🔍 重试前页码检查: 当前第${currentPageCheck}页`);

              // 如果发现页码已经变化了，说明翻页其实成功了
              if (currentPageCheck > currentPage) {
                console.log(`✅ 发现页码已经变化到第${currentPageCheck}页，翻页实际成功`);
                flipSuccess = true;
                break;
              }
            } catch (error) {
              console.warn('⚠️ 页面状态检查失败:', error);
            }
          }
        }
      }

      if (!flipSuccess) {
        console.error(`❌ 经过${maxFlipRetries}次尝试，仍无法翻到下一页`);

        // 最后检查一次是否真的无法翻页
        const finalCurrentPage = getCurrentPage();
        const finalTotalPages = getTotalPages();

        console.log(`🔍 最终检查: 当前第${finalCurrentPage}页，总共${finalTotalPages}页`);

        if (finalCurrentPage >= finalTotalPages) {
          console.log('✅ 确认已到达最后一页，采集结束');
          updateFloatingWindow({
            log: `已到达最后一页(第${finalCurrentPage}页)，采集结束`
          });
        } else {
          console.warn(`⚠️ 翻页失败但未到最后一页，可能是网络问题或页面结构变化`);
          updateFloatingWindow({
            log: `翻页失败，可能是网络问题，已采集${currentPage}页`
          });
        }
        break;
      }

      console.log(`✅ 成功翻到第 ${currentPage + 1} 页`);
      currentPage++;

      // 等待新页面稳定
      try {
        await waitForPageStable();
      } catch (error) {
        console.warn('⚠️ 等待页面稳定失败，继续采集:', error);
        // 即使等待失败也继续，给页面更多时间
        await new Promise(resolve => setTimeout(resolve, 2000));
      }

      console.timeEnd(`第${currentPage - 1}页采集耗时`);
      console.groupEnd();
    }

    // 采集完成后的最终更新
    const finalMessage = shouldStop ?
      `采集已手动停止！\n已采集 ${searchData.length} 条商品数据\n当前第 ${currentPage} 页` :
      `采集完成！\n共采集 ${searchData.length} 条商品数据\n涉及 ${currentPage} 页${currentPage < pageCount ? '\n(已到达最大页数)' : ''}`;

    console.log(`🏁 ${finalMessage.replace(/\n/g, ' ')}`);

    updateFloatingWindow({
      progress: 100,
      currentPage,
      totalItems: searchData.length,
      currentPageItems: searchData.length - lastPageItemCount,
      log: finalMessage
    });

    // 显示完成弹窗
    alert(finalMessage);

    // 启用下载按钮
    const floatingWindow = document.getElementById('xy-floating-window');
    if (floatingWindow) {
      const downloadBtn = floatingWindow.querySelector('#xy-download');
      downloadBtn.disabled = false;

      // 更新其他按钮状态
      const startBtn = floatingWindow.querySelector('#xy-start');
      const pauseBtn = floatingWindow.querySelector('#xy-pause');
      const stopBtn = floatingWindow.querySelector('#xy-stop');

      startBtn.disabled = false;
      pauseBtn.disabled = true;
      stopBtn.disabled = true;
    }

    console.timeEnd('搜索采集总耗时');
    console.groupEnd();

  } catch (error) {
    updateFloatingWindow({
      progress: 0,
      currentPage: getCurrentPage(),
      totalItems: searchData.length,
      currentPageItems: 0,
      log: `错误：${error.message}`
    });
    console.error('❌ 采集过程出错：', error);
    console.timeEnd('搜索采集总耗时');
    console.groupEnd();
    alert(`采集过程出现错误：${error.message}`);
  } finally {
    isCollecting = false;

    // 重置按钮状态
    const finalFloatingWindow = document.getElementById('xy-floating-window');
    if (finalFloatingWindow) {
      const startBtn = finalFloatingWindow.querySelector('#xy-start');
      const pauseBtn = finalFloatingWindow.querySelector('#xy-pause');
      const stopBtn = finalFloatingWindow.querySelector('#xy-stop');
      const downloadBtn = finalFloatingWindow.querySelector('#xy-download');

      if (startBtn) startBtn.disabled = false;
      if (pauseBtn) pauseBtn.disabled = true;
      if (stopBtn) stopBtn.disabled = true;
      if (downloadBtn) downloadBtn.disabled = false; // 启用下载按钮

      console.log('🔘 采集完成，按钮状态已重置');
    }
  }
}

// 等待页面稳定
async function waitForPageStable() {
  console.log('⏳ 等待页面稳定...');
  let retryCount = 0;
  const maxRetries = 10;

  while (retryCount < maxRetries) {
    await new Promise(resolve => setTimeout(resolve, 500));

    // 检查是否正在加载
    const isLoading = document.querySelector('.loading-container') !== null;

    // 检查商品列表是否存在
    const items = document.querySelectorAll('.feeds-item-wrap--rGdH_KoF');

    if (!isLoading && items.length > 0) {
      console.log('✅ 页面已稳定');
      return true;
    }

    retryCount++;
    console.log(`⏳ 等待中...（${retryCount}/${maxRetries}）`);
  }

  throw new Error('页面加载超时');
}

// 保存数据到storage
async function saveToStorage() {
  return new Promise((resolve, reject) => {
    chrome.storage.local.set({ 'searchData': searchData }, () => {
      if (chrome.runtime.lastError) {
        reject(chrome.runtime.lastError);
      } else {
        console.log('数据已保存，当前共有', searchData.length, '条记录');
        resolve();
      }
    });
  });
}

// 等待页面滚动加载
async function scrollToLoadImages() {
  return new Promise((resolve) => {
    let lastHeight = document.documentElement.scrollHeight;
    let scrollAttempts = 0;
    const maxAttempts = 10;

    function scroll() {
      window.scrollTo(0, document.documentElement.scrollHeight);
      setTimeout(() => {
        const newHeight = document.documentElement.scrollHeight;
        if (newHeight === lastHeight || scrollAttempts >= maxAttempts) {
          // 滚动到底部或达到最大尝试次数，等待图片加载
          setTimeout(resolve, 1000);
        } else {
          lastHeight = newHeight;
          scrollAttempts++;
          scroll();
        }
      }, 1000); // 每次滚动后等待1秒
    }

    scroll();
  });
}

// 修改采集当前页面数据的函数
async function collectCurrentPageData(minWantCount = 0) {
  await waitForNewPageLoad();

  // 先滚动页面加载所有图片
  console.log('📜 滚动页面加载图片...');
  await scrollToLoadImages();

  // 滚回顶部
  window.scrollTo(0, 0);

  // 获取当前页面所有商品
  const items = document.querySelectorAll('.feeds-item-wrap--rGdH_KoF');
  console.log(`🔍 发现 ${items.length} 个商品`);

  let successCount = 0;
  let filteredCount = 0;
  let errorCount = 0;
  let soldCount = 0;

  for (const item of items) {
    try {
      // 检查是否已售出
      const isSold = item.parentElement.querySelector('.itemStatus--FGs2IDvi') !== null;

      const data = extractItemData(item, minWantCount);
      if (data) {
        // 添加已售出标记
        if (isSold) {
          data.status = '已售出';
          soldCount++;
        } else {
          data.status = '在售';
        }
        searchData.push(data);
        successCount++;
      } else {
        filteredCount++;
      }
    } catch (error) {
      console.error('❌ 商品数据提取错误：', error);
      errorCount++;
    }
  }

  // 更新悬浮窗中的实时统计
  updateFloatingWindow({
    currentPageItems: successCount,
    totalItems: searchData.length,
    log: `当前页：成功${successCount}(已售${soldCount})，过滤${filteredCount}，失败${errorCount}`
  });

  console.log(`📊 当前页采集统计：成功 ${successCount}(已售${soldCount})，被过滤 ${filteredCount}，失败 ${errorCount}`);
}

// 等待新页面加载
async function waitForNewPageLoad() {
  let retryCount = 0;
  const maxRetries = 10;

  while (retryCount < maxRetries) {
    await new Promise(resolve => setTimeout(resolve, 500));
    const activePageNum = document.querySelector('.search-pagination-page-box-active--vsBooIVl');
    if (activePageNum) {
      console.log(`✅ 当前页码: ${activePageNum.textContent}`);
      return;
    }
    retryCount++;
    console.log(`⏳ 等待页面加载...（${retryCount}/${maxRetries}）`);
  }
}

// 提取商品数据
function extractItemData(item, minWantCount = 0) {
  try {
    // 获取标题
    const titleElement = item.querySelector('.main-title--sMrtWSJa');
    const title = titleElement ? titleElement.textContent.trim() : '';

    // 获取商品链接
    const link = item.getAttribute('href');

    // 从链接中提取商品ID
    let itemId = null;
    if (link) {
      // 尝试多种ID提取方式
      const idMatches = [
        link.match(/id=(\d+)/),           // 标准格式: ?id=123456
        link.match(/\/item\/(\d+)/),      // 路径格式: /item/123456
        link.match(/\/(\d+)$/),           // 末尾数字: /123456
        link.match(/itemId=(\d+)/),       // itemId参数: ?itemId=123456
      ];

      for (const match of idMatches) {
        if (match && match[1]) {
          itemId = match[1];
          console.log(`🔍 提取到商品ID: ${itemId}, 链接: ${link}`);
          break;
        }
      }

      if (!itemId) {
        console.warn(`⚠️ 无法从链接提取商品ID: ${link}`);
      }
    }

    // 获取商品图片
    const imageElement = item.querySelector('.feeds-image--TDRC4fV1');
    let image = '';
    if (imageElement) {
      // 处理图片地址
      image = imageElement.src
        .replace(/^\/\//, 'https://');  // 添加协议

      // 提取原始图片地址（移除所有压缩和格式转换参数）
      const matches = image.match(/(.*?(?:fleamarket|mytaobao).*?)(?:_\d+x\d+.*|$)/);
      if (matches && matches[1]) {
        image = matches[1];

        // 确保图片有正确的扩展名
        if (image.includes('-fleamarket.heic')) {
          if (!image.endsWith('.heic')) {
            image += '.heic';
          }
        } else if (image.includes('-fleamarket.jpg') || image.includes('-0-fleamarket')) {
          if (!image.endsWith('.jpg')) {
            image += '.jpg';
          }
        } else if (!image.match(/\.(jpg|jpeg|png|gif|heic)$/i)) {
          // 默认添加.jpg后缀
          image += '.jpg';
        }
      }

      // 处理特殊情况：如果地址包含 mytaobao，替换为 fleamarket
      image = image.replace('mytaobao', 'fleamarket');
    }

    // 获取价格
    const priceWrap = item.querySelector('.price-wrap--YzmU5cUl');
    let price = '';
    if (priceWrap) {
      const integerPart = priceWrap.querySelector('.number--NKh1vXWM')?.textContent.trim() || '';
      const decimalPart = priceWrap.querySelector('.decimal--lSAcITCN')?.textContent.trim() || '';
      price = integerPart + (decimalPart ? decimalPart : '');
    }

    // 获取想要人数
    const wantCount = extractWantCount(item);

    // 如果想要人数小于最小要求，则跳过
    if (minWantCount > 0 && parseInt(wantCount || '0') < minWantCount) {
      return null;
    }

    // 尝试从API缓存中获取上架时间
    let publishTime = '';
    if (itemId) {
      const cachedData = apiDataCache.get(`search_${itemId}`);
      if (cachedData && cachedData.proPolishTime) {
        try {
          // proPolishTime 是时间戳，需要转换
          const publishDate = new Date(parseInt(cachedData.proPolishTime));
          publishTime = publishDate.toLocaleString('zh-CN', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit'
          });
          console.log(`⏰ 从API缓存获取商品 ${itemId} 的上架时间: ${publishTime}`);
        } catch (error) {
          console.warn(`⚠️ 商品 ${itemId} 时间转换失败:`, error);
        }
      } else {
        console.warn(`⚠️ 商品 ${itemId} 在API缓存中未找到上架时间数据`);
        console.log(`📊 当前缓存状态: ${apiDataCache.size} 条记录`);
      }
    } else {
      console.warn('⚠️ 无法获取商品ID，无法查询上架时间');
    }

    return {
      title,
      price,
      wantCount,
      link,
      image,
      status: '在售',
      publishTime,
      itemId
    };
  } catch (error) {
    console.warn('提取商品数据失败:', error);
    return null;
  }
}

// 提取想要人数
function extractWantCount(item) {
  try {
    const wantElement = item.querySelector('.text--MaM9Cmdn[title*="人想要"]');
    if (wantElement) {
      return wantElement.getAttribute('title').replace('人想要', '');
    }
    return '0';
  } catch (error) {
    console.warn('提取想要人数失败:', error);
    return '0';
  }
}

// 提取卖家地区
function extractLocation(item) {
  try {
    const locationElement = item.querySelector('.seller-text--Rr2Y3EbB');
    return locationElement ? locationElement.textContent.trim() : '';
  } catch (error) {
    console.warn('提取地区失败:', error);
    return '';
  }
}

// 获取总页数
function getTotalPages() {
  try {
    // 方法1：查找分页器中的页码元素
    const pageBoxes = document.querySelectorAll('.search-pagination-page-box--AbqmJFFp');
    if (pageBoxes.length > 0) {
      // 查找最后一个数字页码
      let lastPage = 1;
      for (let i = pageBoxes.length - 1; i >= 0; i--) {
        const pageText = pageBoxes[i].textContent.trim();
        const pageNum = parseInt(pageText);
        if (!isNaN(pageNum) && !pageText.includes('...')) {
          lastPage = Math.max(lastPage, pageNum);
        }
      }
      console.log(`📑 通过页码元素检测到总页数: ${lastPage}`);
      return lastPage;
    }

    // 方法2：查找"共X页"的文本
    const pageInfoElements = document.querySelectorAll('*');
    for (const element of pageInfoElements) {
      const text = element.textContent;
      if (text && text.includes('共') && text.includes('页')) {
        const match = text.match(/共\s*(\d+)\s*页/);
        if (match) {
          const totalPages = parseInt(match[1]);
          console.log(`📑 通过页面信息检测到总页数: ${totalPages}`);
          return totalPages;
        }
      }
    }

    // 方法3：查找输入框的最大值
    const pageInput = document.querySelector('.search-pagination-to-page-input--NDqqDgSl');
    if (pageInput && pageInput.max) {
      const maxPages = parseInt(pageInput.max);
      if (!isNaN(maxPages)) {
        console.log(`📑 通过输入框最大值检测到总页数: ${maxPages}`);
        return maxPages;
      }
    }

    console.log('📑 无法检测总页数，默认返回1');
    return 1;
  } catch (error) {
    console.error('❌ 获取总页数失败:', error);
    return 1;
  }
}

// 获取当前页码
function getCurrentPage() {
  try {
    // 方法1：查找激活的页码元素
    const activePageBox = document.querySelector('.search-pagination-page-box-active--vsBooIVl');
    if (activePageBox) {
      const pageNum = parseInt(activePageBox.textContent.trim());
      if (!isNaN(pageNum)) {
        console.log(`📄 当前页码: ${pageNum}`);
        return pageNum;
      }
    }

    // 方法2：查找输入框的当前值
    const pageInput = document.querySelector('.search-pagination-to-page-input--NDqqDgSl');
    if (pageInput && pageInput.value) {
      const pageNum = parseInt(pageInput.value);
      if (!isNaN(pageNum)) {
        console.log(`📄 从输入框获取当前页码: ${pageNum}`);
        return pageNum;
      }
    }

    // 方法3：从URL参数获取
    const urlParams = new URLSearchParams(window.location.search);
    const pageParam = urlParams.get('page') || urlParams.get('p');
    if (pageParam) {
      const pageNum = parseInt(pageParam);
      if (!isNaN(pageNum)) {
        console.log(`📄 从URL参数获取当前页码: ${pageNum}`);
        return pageNum;
      }
    }

    console.log('📄 无法检测当前页码，默认返回1');
    return 1;
  } catch (error) {
    console.error('❌ 获取当前页码失败:', error);
    return 1;
  }
}

// 翻到下一页
async function goToNextPage() {
  try {
    const currentPageNum = getCurrentPage();
    const totalPages = getTotalPages();

    console.log(`📄 准备翻页: 当前第${currentPageNum}页，总共${totalPages}页`);

    // 检查是否已经是最后一页
    if (currentPageNum >= totalPages) {
      console.log('⚠️ 已经是最后一页，无法继续翻页');
      return false;
    }

    const targetPage = currentPageNum + 1;
    console.log(`🎯 目标页码: ${targetPage}`);

    // 方法1：尝试点击下一页按钮
    const nextPageSuccess = await tryClickNextPage(currentPageNum, targetPage);
    if (nextPageSuccess) {
      return true;
    }

    // 方法2：尝试点击具体页码
    const clickPageSuccess = await tryClickSpecificPage(targetPage, currentPageNum);
    if (clickPageSuccess) {
      return true;
    }

    // 方法3：尝试使用输入框跳转
    const inputPageSuccess = await tryInputPageJump(targetPage, currentPageNum);
    if (inputPageSuccess) {
      return true;
    }

    // 方法4：尝试修改URL参数
    const urlPageSuccess = await tryUrlPageJump(targetPage, currentPageNum);
    if (urlPageSuccess) {
      return true;
    }

    console.error('❌ 所有翻页方式都失败');
    return false;

  } catch (error) {
    console.error('❌ 翻页过程出错:', error);
    return false;
  }
}

// 尝试点击下一页按钮
async function tryClickNextPage(currentPageNum, targetPage) {
  try {
    console.log('🖱️ 方法1: 尝试点击下一页按钮');

    // 查找下一页按钮 - 针对闲鱼网站的特殊选择器
    const nextButtons = [
      // 闲鱼特有的下一页按钮
      document.querySelector('.search-pagination-next--Ej8Ej8Ej'),
      document.querySelector('.pagination-next'),
      document.querySelector('[class*="pagination-next"]'),
      document.querySelector('[class*="next"]'),
      // 通用的下一页按钮
      document.querySelector('[aria-label="下一页"]'),
      document.querySelector('[aria-label="Next"]'),
      document.querySelector('.next-page'),
      document.querySelector('.next'),
      // 通过文本内容查找
      ...Array.from(document.querySelectorAll('button, a, span, div')).filter(el => {
        const text = el.textContent?.trim();
        return text && (
          text === '下一页' ||
          text === 'Next' ||
          text === '>' ||
          text === '›' ||
          text === '»'
        );
      }),
      // 查找包含箭头图标的元素
      ...Array.from(document.querySelectorAll('[class*="arrow"], [class*="icon"]')).filter(el => {
        const classList = Array.from(el.classList);
        return classList.some(cls =>
          cls.includes('right') ||
          cls.includes('next') ||
          cls.includes('forward')
        );
      })
    ];

    console.log(`🔍 找到 ${nextButtons.length} 个可能的下一页按钮`);

    for (let i = 0; i < nextButtons.length; i++) {
      const nextBtn = nextButtons[i];
      if (!nextBtn) continue;

      // 检查按钮是否可点击
      const isDisabled = nextBtn.disabled ||
        nextBtn.classList.contains('disabled') ||
        nextBtn.classList.contains('inactive') ||
        nextBtn.getAttribute('aria-disabled') === 'true';

      if (isDisabled) {
        console.log(`⚠️ 按钮${i + 1}被禁用，跳过`);
        continue;
      }

      // 检查按钮是否可见
      const rect = nextBtn.getBoundingClientRect();
      if (rect.width === 0 || rect.height === 0) {
        console.log(`⚠️ 按钮${i + 1}不可见，跳过`);
        continue;
      }

      console.log(`🖱️ 尝试点击按钮${i + 1}: ${nextBtn.textContent?.trim() || nextBtn.className}`);

      // 滚动到按钮位置确保可见
      nextBtn.scrollIntoView({ behavior: 'smooth', block: 'center' });
      await new Promise(resolve => setTimeout(resolve, 500));

      // 尝试多种点击方式
      try {
        // 方式1：直接点击
        nextBtn.click();
        await new Promise(resolve => setTimeout(resolve, 1000));

        // 检查是否成功
        let success = await waitForPageChange(currentPageNum, targetPage, 10);
        if (success) {
          console.log('✅ 直接点击成功');
          return true;
        }

        // 方式2：模拟鼠标事件
        const clickEvent = new MouseEvent('click', {
          bubbles: true,
          cancelable: true,
          view: window
        });
        nextBtn.dispatchEvent(clickEvent);
        await new Promise(resolve => setTimeout(resolve, 1000));

        success = await waitForPageChange(currentPageNum, targetPage, 10);
        if (success) {
          console.log('✅ 模拟点击成功');
          return true;
        }

        // 方式3：如果是链接，尝试导航
        if (nextBtn.tagName === 'A' && nextBtn.href) {
          console.log('🔗 尝试链接导航');
          window.location.href = nextBtn.href;
          await new Promise(resolve => setTimeout(resolve, 2000));

          success = await waitForPageChange(currentPageNum, targetPage, 15);
          if (success) {
            console.log('✅ 链接导航成功');
            return true;
          }
        }

      } catch (error) {
        console.warn(`⚠️ 点击按钮${i + 1}出错:`, error);
      }
    }

    console.log('⚠️ 所有下一页按钮都尝试失败');
    return false;
  } catch (error) {
    console.error('❌ 点击下一页按钮出错:', error);
    return false;
  }
}

// 尝试点击具体页码
async function tryClickSpecificPage(targetPage, currentPageNum) {
  try {
    console.log(`🖱️ 方法2: 尝试点击页码 ${targetPage}`);

    const pageBoxes = Array.from(document.querySelectorAll('.search-pagination-page-box--AbqmJFFp'));

    for (const pageBox of pageBoxes) {
      const pageText = pageBox.textContent.trim();
      const pageNum = parseInt(pageText);

      if (pageNum === targetPage && !pageBox.classList.contains('search-pagination-page-box-active--vsBooIVl')) {
        console.log(`🖱️ 找到目标页码 ${targetPage}，尝试点击`);
        pageBox.click();

        const success = await waitForPageChange(currentPageNum, targetPage, 15);
        if (success) {
          console.log('✅ 页码点击成功');
          return true;
        }
      }
    }

    console.log('⚠️ 页码点击失败');
    return false;
  } catch (error) {
    console.error('❌ 点击页码出错:', error);
    return false;
  }
}

// 尝试使用输入框跳转
async function tryInputPageJump(targetPage, currentPageNum) {
  try {
    console.log(`🖱️ 方法3: 尝试输入框跳转到第 ${targetPage} 页`);

    const input = document.querySelector('.search-pagination-to-page-input--NDqqDgSl');
    const confirmButton = document.querySelector('.search-pagination-to-page-confirm-button--b51GmTKS');

    if (input && confirmButton) {
      console.log('🖱️ 找到输入框和确认按钮');

      // 清空并输入目标页码
      input.focus();
      input.select();
      input.value = '';
      input.value = targetPage.toString();

      // 触发输入事件
      input.dispatchEvent(new Event('input', { bubbles: true }));
      input.dispatchEvent(new Event('change', { bubbles: true }));

      await new Promise(resolve => setTimeout(resolve, 200));

      // 点击确认按钮
      confirmButton.click();

      const success = await waitForPageChange(currentPageNum, targetPage, 15);
      if (success) {
        console.log('✅ 输入框跳转成功');
        return true;
      }
    }

    console.log('⚠️ 输入框跳转失败');
    return false;
  } catch (error) {
    console.error('❌ 输入框跳转出错:', error);
    return false;
  }
}

// 尝试修改URL参数跳转
async function tryUrlPageJump(targetPage, currentPageNum) {
  try {
    console.log(`🖱️ 方法4: 尝试URL参数跳转到第 ${targetPage} 页`);

    const currentUrl = new URL(window.location.href);
    const params = currentUrl.searchParams;

    // 尝试不同的页码参数名
    const pageParams = ['page', 'p', 'pageNum', 'pageIndex'];
    let paramSet = false;

    for (const param of pageParams) {
      if (params.has(param)) {
        params.set(param, targetPage.toString());
        paramSet = true;
        break;
      }
    }

    // 如果没有找到现有的页码参数，添加一个
    if (!paramSet) {
      params.set('page', targetPage.toString());
    }

    const newUrl = currentUrl.toString();
    console.log(`🔗 新URL: ${newUrl}`);

    // 使用pushState避免页面刷新
    window.history.pushState({}, '', newUrl);

    // 触发页面更新事件
    window.dispatchEvent(new PopStateEvent('popstate'));

    const success = await waitForPageChange(currentPageNum, targetPage, 10);
    if (success) {
      console.log('✅ URL参数跳转成功');
      return true;
    }

    console.log('⚠️ URL参数跳转失败');
    return false;
  } catch (error) {
    console.error('❌ URL参数跳转出错:', error);
    return false;
  }
}

// 等待页面变化
async function waitForPageChange(currentPageNum, targetPage, maxRetries = 20) {
  console.log(`⏳ 等待页面从第${currentPageNum}页变化到第${targetPage}页...`);

  let lastUrl = window.location.href;
  let lastPageContent = document.querySelector('.feeds-list-container--UkIMBPNk')?.innerHTML || '';

  for (let retryCount = 0; retryCount < maxRetries; retryCount++) {
    await new Promise(resolve => setTimeout(resolve, 500));

    // 检查1：是否正在加载
    const loadingSelectors = [
      '.loading-container',
      '.loading',
      '[class*="loading"]',
      '[class*="spinner"]',
      '[class*="Loading"]'
    ];

    const isLoading = loadingSelectors.some(selector =>
      document.querySelector(selector) !== null
    );

    if (isLoading) {
      console.log(`⏳ 页面加载中... (${retryCount + 1}/${maxRetries})`);
      continue;
    }

    // 检查2：URL是否变化
    const currentUrl = window.location.href;
    const urlChanged = currentUrl !== lastUrl;

    // 检查3：页码是否变化
    const newPageNum = getCurrentPage();
    const pageNumChanged = newPageNum !== currentPageNum;

    // 检查4：页面内容是否变化
    const currentPageContent = document.querySelector('.feeds-list-container--UkIMBPNk')?.innerHTML || '';
    const contentChanged = currentPageContent !== lastPageContent && currentPageContent.length > 0;

    // 检查5：商品列表是否存在且有内容
    const items = document.querySelectorAll('.feeds-item-wrap--rGdH_KoF');
    const hasItems = items.length > 0;

    console.log(`🔍 检查状态 (${retryCount + 1}/${maxRetries}): 页码${newPageNum}, URL变化${urlChanged}, 内容变化${contentChanged}, 商品数${items.length}`);

    // 成功条件：页码变化到目标页且有商品数据
    if (newPageNum === targetPage && hasItems) {
      console.log(`✅ 页面成功变化到第 ${newPageNum} 页，发现 ${items.length} 个商品`);

      // 额外等待确保页面内容完全加载
      await new Promise(resolve => setTimeout(resolve, 1000));

      // 再次检查确保数据稳定
      const finalItems = document.querySelectorAll('.feeds-item-wrap--rGdH_KoF');
      if (finalItems.length > 0) {
        console.log(`✅ 页面内容稳定，最终发现 ${finalItems.length} 个商品`);
        return true;
      } else {
        console.log('⚠️ 页面内容不稳定，继续等待...');
      }
    }

    // 部分成功条件：页码变化了但不是目标页（可能跳过了）
    else if (pageNumChanged && newPageNum > currentPageNum) {
      console.log(`⚠️ 页面变化到第 ${newPageNum} 页，但目标是第 ${targetPage} 页`);

      if (hasItems) {
        console.log(`✅ 虽然不是目标页，但页面有效且有商品数据，接受此结果`);
        await new Promise(resolve => setTimeout(resolve, 1000));
        return true;
      }
    }

    // 检查是否有其他变化迹象
    else if (urlChanged || contentChanged) {
      console.log(`🔄 检测到页面变化迹象，继续等待页码更新...`);
      lastUrl = currentUrl;
      lastPageContent = currentPageContent;

      // 给更多时间让页码更新
      await new Promise(resolve => setTimeout(resolve, 1000));
      continue;
    }

    // 检查是否到达了最后一页
    const totalPages = getTotalPages();
    if (currentPageNum >= totalPages && newPageNum === currentPageNum) {
      console.log(`⚠️ 已到达最后一页(第${currentPageNum}页)，无法继续翻页`);
      return false;
    }

    console.log(`⏳ 等待页面变化... (${retryCount + 1}/${maxRetries})`);
  }

  // 超时后的最终检查
  const finalPageNum = getCurrentPage();
  const finalItems = document.querySelectorAll('.feeds-item-wrap--rGdH_KoF');

  console.log(`⏰ 等待超时，最终状态: 页码${finalPageNum}, 商品数${finalItems.length}`);

  // 如果页码有变化且有商品，也算成功
  if (finalPageNum > currentPageNum && finalItems.length > 0) {
    console.log(`✅ 超时但检测到有效变化，页码从${currentPageNum}变为${finalPageNum}`);
    return true;
  }

  console.log('❌ 等待页面变化超时且无有效变化');
  return false;
}

// 统一的HTML生成函数
function generateUnifiedHTML(options) {
  const { title, data, hasDetailData, timestamp } = options;

  let htmlContent = `
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="UTF-8">
      <title>${title}</title>
      <!-- 引入必要的库文件 -->
      <script src="https://cdnjs.cloudflare.com/ajax/libs/jszip/3.10.1/jszip.min.js"></script>
      <script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>
      <style>
        body {
          margin: 0;
          padding: 20px;
          font-family: Arial, sans-serif;
          background: #f8f9fa;
        }
        h1 {
          color: #333;
          text-align: center;
          margin-bottom: 20px;
          padding: 20px;
          background: white;
          box-shadow: 0 1px 3px rgba(0,0,0,0.1);
          border-radius: 8px;
        }
        .stats {
          margin: 20px 0;
          padding: 15px;
          background: white;
          border-radius: 8px;
          box-shadow: 0 1px 3px rgba(0,0,0,0.1);
          text-align: center;
          color: #666;
        }
        
        /* 导出工具栏样式 */
        .export-toolbar {
          margin: 20px 0;
          padding: 20px;
          background: white;
          border-radius: 8px;
          box-shadow: 0 1px 3px rgba(0,0,0,0.1);
          text-align: center;
          display: flex;
          flex-wrap: wrap;
          gap: 15px;
          justify-content: center;
          align-items: center;
        }
        
        .export-btn {
          padding: 12px 24px;
          border: none;
          border-radius: 6px;
          cursor: pointer;
          font-size: 14px;
          font-weight: 600;
          transition: all 0.3s ease;
          text-decoration: none;
          display: inline-flex;
          align-items: center;
          gap: 8px;
          box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .export-btn:hover {
          transform: translateY(-2px);
          box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        
        .export-btn-images {
          background: linear-gradient(135deg, #52c41a 0%, #73d13d 100%);
          color: white;
        }
        
        .export-btn-excel {
          background: linear-gradient(135deg, #1890ff 0%, #40a9ff 100%);
          color: white;
        }
        
        .export-btn-combined {
          background: linear-gradient(135deg, #fa8c16 0%, #ffa940 100%);
          color: white;
        }
        
        .export-progress {
          display: none;
          margin: 10px 0;
          padding: 10px;
          background: #f0f0f0;
          border-radius: 4px;
          text-align: center;
          font-size: 14px;
        }
        
        .progress-bar {
          width: 100%;
          height: 8px;
          background: #e8e8e8;
          border-radius: 4px;
          overflow: hidden;
          margin: 10px 0;
        }
        
        .progress-fill {
          height: 100%;
          background: linear-gradient(90deg, #1890ff, #40a9ff);
          width: 0%;
          transition: width 0.3s ease;
        }
        table {
          border-collapse: collapse;
          width: 100%;
          margin: 0 auto;
          background: white;
          box-shadow: 0 1px 3px rgba(0,0,0,0.1);
          border-radius: 8px;
          overflow: hidden;
        }
        th, td {
          padding: 15px;
          text-align: left;
          vertical-align: middle;
          border-bottom: 1px solid #eee;
          word-break: break-word;
        }
        th {
          background-color: #f8f9fa;
          font-weight: bold;
          color: #333;
          white-space: nowrap;
          position: relative;
        }
        tr:hover {
          background-color: #f5f5f5;
        }
        .image-cell {
          width: 200px;
          text-align: center;
        }
        .image-container {
          width: 200px;
          height: 200px;
          display: flex;
          align-items: center;
          justify-content: center;
          background: #f8f9fa;
          border-radius: 4px;
          overflow: hidden;
          margin: 0 auto;
        }
        .product-image {
          max-width: 100%;
          max-height: 100%;
          object-fit: contain;
          transition: transform 0.3s;
        }
        .product-image:hover {
          transform: scale(1.1);
        }
        .title-cell {
          width: 40%;
          white-space: normal;
          overflow: visible;
          text-overflow: clip;
        }
        .price-cell {
          width: 100px;
          color: #ff4d4f;
          font-weight: bold;
        }
        .want-cell, .view-cell, .conversion-cell {
          text-align: center;
          width: 100px;
        }
        .status-cell {
          text-align: center;
          width: 80px;
        }
        .publish-time-cell {
          text-align: center;
          width: 150px;
          font-size: 14px;
        }
        .link-cell {
          width: 100px;
          text-align: center;
        }
        .status-sold {
          color: #ff4d4f;
        }
        .status-selling {
          color: #52c41a;
        }
        a {
          color: #1890ff;
          text-decoration: none;
          padding: 6px 12px;
          background: #e6f7ff;
          border-radius: 4px;
          transition: all 0.3s;
        }
        a:hover {
          background: #1890ff;
          color: white;
        }
        .sortable {
          cursor: pointer;
          user-select: none;
          position: relative;
        }
        .sortable:hover {
          background-color: #e6f7ff;
        }
        .sort-indicator {
          position: absolute;
          right: 5px;
          top: 50%;
          transform: translateY(-50%);
          font-size: 12px;
          color: #666;
        }
        .sort-asc .sort-indicator::after {
          content: '↑';
          color: #1890ff;
        }
        .sort-desc .sort-indicator::after {
          content: '↓';
          color: #1890ff;
        }
        .detail-toggle {
          cursor: pointer;
          color: #1890ff;
          text-decoration: underline;
          background: none;
          border: none;
          font-size: 14px;
          padding: 0;
        }
        .detail-row {
          background-color: #fafafa;
        }
        .detail-cell {
          padding: 20px;
        }
        .detail-content {
          margin-top: 10px;
          white-space: pre-wrap;
          font-size: 14px;
          line-height: 1.6;
          border-top: 1px solid #eee;
          padding-top: 15px;
        }
        .detail-stat {
          display: inline-block;
          margin-right: 15px;
          background: #f0f0f0;
          padding: 5px 10px;
          border-radius: 4px;
          font-size: 12px;
          color: #666;
        }
        .detail-info {
          margin-bottom: 10px;
        }
        .image-gallery {
          display: flex;
          flex-wrap: wrap;
          gap: 10px;
          margin-top: 10px;
        }
        .gallery-item {
          width: 80px;
          height: 80px;
        }
        .gallery-item img {
          width: 100%;
          height: 100%;
          object-fit: cover;
          border-radius: 4px;
        }
        .hidden-detail {
          display: none;
        }
        .footer {
          margin-top: 30px;
          padding: 20px;
          text-align: center;
          border-top: 1px solid #eee;
          color: #666;
        }
        .footer a {
          color: #1890ff;
          text-decoration: none;
        }
        .footer a:hover {
          text-decoration: underline;
        }
      </style>
      <script>
        let sortDirection = {};
        
        // 商品数据 - 用于导出功能
        const productData = ${JSON.stringify(data, null, 2)};
        
        function handleImageError(img) {
          const container = img.parentElement;
          container.innerHTML = '<div style="width:100%;height:100%;display:flex;align-items:center;justify-content:center;color:#999;font-size:12px;text-align:center;background:#f0f0f0;">图片加载失败</div>';
        }
        
        function toggleDetail(itemId) {
          const detailRow = document.getElementById('detail-' + itemId);
          if (detailRow) {
            detailRow.classList.toggle('hidden-detail');
          }
        }
        
        function getCellValue(row, columnIndex) {
          const cell = row.cells[columnIndex];
          if (!cell) return 0;
          
          let text = cell.textContent.trim();
          
          // 处理转化比（百分号）
          if (text.includes('%')) {
            return parseFloat(text.replace('%', '')) || 0;
          }
          
          // 处理"无数据" - 给一个特殊的负值，这样在排序时会排在最后
          if (text === '无数据' || text === '无') {
            return -999999;
          }
          
          // 处理数字 - 修复正则表达式错误
          const num = parseFloat(text.replace(/[^\d.-]/g, '')) || 0;
          return num;
        }
        
        function sortTable(columnIndex) {
          const table = document.querySelector('table');
          if (!table) {
            console.error('未找到表格');
            return;
          }
          
          // 确保表格有正确的结构
          let tbody = table.querySelector('tbody');
          if (!tbody) {
            console.warn('未找到tbody，尝试创建');
            // 如果没有tbody，创建一个并移动所有非表头行到tbody中
            tbody = document.createElement('tbody');
            const allRows = Array.from(table.rows);
            // 跳过第一行（表头）
            for (let i = 1; i < allRows.length; i++) {
              tbody.appendChild(allRows[i]);
            }
            table.appendChild(tbody);
          }
          
          const rows = Array.from(tbody.rows);
          console.log('tbody中的行数:', rows.length);
          
          // 分离数据行和详情行
          const dataRows = [];
          const detailRows = {};
          
          for (let i = 0; i < rows.length; i++) {
            const row = rows[i];
            if (row.id && row.id.startsWith('detail-')) {
              const index = row.id.replace('detail-', '');
              detailRows[index] = row;
            } else {
              dataRows.push({row: row, index: dataRows.length});
            }
          }
          
          console.log('数据行数量:', dataRows.length);
          console.log('详情行数量:', Object.keys(detailRows).length);
          
          // 确定排序方向
          const currentDirection = sortDirection[columnIndex] || 'asc';
          const newDirection = currentDirection === 'asc' ? 'desc' : 'asc';
          sortDirection[columnIndex] = newDirection;
          
          console.log('排序列:', columnIndex, '排序方向:', newDirection);
          
          // 更新表头排序指示器
          const headers = table.querySelectorAll('thead th.sortable');
          if (headers.length === 0) {
            // 如果没有找到thead中的表头，尝试查找第一行的表头
            const firstRowHeaders = table.querySelectorAll('tr:first-child th.sortable');
            console.log('使用第一行表头，数量:', firstRowHeaders.length);
            firstRowHeaders.forEach((header, index) => {
              header.classList.remove('sort-asc', 'sort-desc');
              const actualColumnIndex = index === 0 ? 4 : (index === 1 ? 5 : 6);
              if (actualColumnIndex === columnIndex) {
                header.classList.add(newDirection === 'asc' ? 'sort-asc' : 'sort-desc');
              }
            });
          } else {
            console.log('可排序表头数量:', headers.length);
            headers.forEach((header, index) => {
              header.classList.remove('sort-asc', 'sort-desc');
              const actualColumnIndex = index === 0 ? 4 : (index === 1 ? 5 : 6);
              if (actualColumnIndex === columnIndex) {
                header.classList.add(newDirection === 'asc' ? 'sort-asc' : 'sort-desc');
              }
            });
          }
          
          // 排序数据行 - 改进排序逻辑
          dataRows.sort((a, b) => {
            const aValue = getCellValue(a.row, columnIndex);
            const bValue = getCellValue(b.row, columnIndex);
            
            // 如果两个值都是"无数据"（-999999），则按想要人数进行二级排序
            if (aValue === -999999 && bValue === -999999) {
              const aWantCount = getCellValue(a.row, 4); // 想要人数在第4列
              const bWantCount = getCellValue(b.row, 4);
              if (newDirection === 'asc') {
                return aWantCount - bWantCount;
              } else {
                return bWantCount - aWantCount;
              }
            }
            
            // 如果一个是"无数据"，另一个不是，"无数据"排在后面
            if (aValue === -999999 && bValue !== -999999) {
              return newDirection === 'asc' ? 1 : 1; // 无数据总是排在后面
            }
            if (bValue === -999999 && aValue !== -999999) {
              return newDirection === 'asc' ? -1 : -1; // 无数据总是排在后面
            }
            
            // 正常的数值排序
            if (newDirection === 'asc') {
              return aValue - bValue;
            } else {
              return bValue - aValue;
            }
          });
          
          // 清空tbody内容（确保不影响表头）
          tbody.innerHTML = '';
          
          // 重新插入排序后的行
          dataRows.forEach((item, newIndex) => {
            // 更新序号
            item.row.cells[0].textContent = newIndex + 1;
            tbody.appendChild(item.row);
            
            // 如果有对应的详情行，也插入
            if (detailRows[item.index]) {
              const detailRow = detailRows[item.index];
              detailRow.id = 'detail-' + newIndex;
              
              // 更新详情按钮的onclick
              const toggleBtn = item.row.querySelector('.detail-toggle');
              if (toggleBtn) {
                toggleBtn.setAttribute('onclick', 'toggleDetail(' + newIndex + ')');
              }
              
              tbody.appendChild(detailRow);
            }
          });
          
          console.log('排序完成，表头应该保持不变');
        }
        
        // 显示进度
        function showProgress(message, progress = 0) {
          const progressDiv = document.getElementById('export-progress');
          const progressFill = document.querySelector('.progress-fill');
          const progressText = progressDiv.querySelector('.progress-text');
          
          progressDiv.style.display = 'block';
          progressFill.style.width = progress + '%';
          if (progressText) {
            progressText.textContent = message;
          } else {
            progressDiv.innerHTML = '<div class="progress-text">' + message + '</div><div class="progress-bar"><div class="progress-fill" style="width:' + progress + '%"></div></div>';
          }
        }
        
        function hideProgress() {
          const progressDiv = document.getElementById('export-progress');
          progressDiv.style.display = 'none';
        }
        
        // 将图片URL转换为blob
        async function urlToBlob(url) {
          try {
            const response = await fetch(url, { mode: 'cors' });
            if (!response.ok) throw new Error('Network response was not ok');
            return await response.blob();
          } catch (error) {
            console.warn('图片下载失败:', url, error);
            return null;
          }
        }
        
        // 导出图片压缩包
        async function exportImages() {
          showProgress('正在准备图片下载...', 0);

          const zip = new JSZip();
          const imgFolder = zip.folder("商品图片");

          let successCount = 0;
          let totalImages = 0;

          // 收集所有图片URL，按商品分组
          const imagesByProduct = [];
          const usedFolderNames = new Set(); // 用于跟踪已使用的文件夹名称

          productData.forEach((item, index) => {
            const productImages = [];

            // 清理商品名称，用作文件夹名 - 优先使用完整标题
            let cleanTitle = (item.fullTitle || item.title || '商品' + (index + 1))
              .substring(0, 50)  // 增加长度限制到50字符
              .replace(/[\\/:*?"<>|]/g, '')  // 移除非法字符
              .replace(/\s+/g, ' ')  // 合并多个空格
              .trim();

            // 处理重复的文件夹名称
            let finalFolderName = cleanTitle;
            let counter = 1;
            while (usedFolderNames.has(finalFolderName)) {
              finalFolderName = cleanTitle + '_' + counter;
              counter++;
            }
            usedFolderNames.add(finalFolderName);

            // 主图
            if (item.image) {
              productImages.push({
                url: item.image,
                filename: '主图.jpg',
                type: 'main'
              });
            }

            // 详情图片 - 确保不重复添加主图
            if (item.images && Array.isArray(item.images)) {
              item.images.forEach((imgUrl, imgIndex) => {
                // 检查是否与主图重复
                if (imgUrl !== item.image) {
                  productImages.push({
                    url: imgUrl,
                    filename: '详情图_' + (imgIndex + 1) + '.jpg',
                    type: 'detail'
                  });
                }
              });
            }

            // 如果没有详情图片，但有主图，至少确保主图被包含
            if (productImages.length === 1 && productImages[0].type === 'main') {
              console.log('警告: 商品 "' + cleanTitle + '" 只有主图，没有详情图片');
            }

            if (productImages.length > 0) {
              imagesByProduct.push({
                folderName: finalFolderName,
                images: productImages,
                productIndex: index + 1
              });
            }
          });

          // 计算总图片数
          totalImages = imagesByProduct.reduce((total, product) => total + product.images.length, 0);

          if (totalImages === 0) {
            alert('没有找到可下载的图片！');
            hideProgress();
            return;
          }

          // 按商品下载图片并添加到对应文件夹
          let processedImages = 0;
          for (const product of imagesByProduct) {
            // 为每个商品创建子文件夹
            const productFolder = imgFolder.folder(product.folderName);

            for (const imageInfo of product.images) {
              processedImages++;
              showProgress('正在下载图片 ' + processedImages + '/' + totalImages + '...', Math.round((processedImages / totalImages) * 80));

              const blob = await urlToBlob(imageInfo.url);
              if (blob) {
                productFolder.file(imageInfo.filename, blob);
                successCount++;
              }

              // 避免请求过快
              if (processedImages % 5 === 0) {
                await new Promise(resolve => setTimeout(resolve, 100));
              }
            }
          }

          showProgress('正在生成压缩包...', 90);

          // 生成压缩包
          const content = await zip.generateAsync({type:"blob"});

          // 创建下载链接
          const link = document.createElement('a');
          link.href = URL.createObjectURL(content);
          link.download = 'XY商品图片_' + new Date().toISOString().slice(0,10) + '.zip';
          document.body.appendChild(link);
          link.click();
          document.body.removeChild(link);

          showProgress('下载完成！', 100);
          setTimeout(() => {
            hideProgress();
            alert('图片压缩包下载完成！\\n成功下载: ' + successCount + '/' + totalImages + ' 张图片\\n已按商品分类到 ' + imagesByProduct.length + ' 个文件夹');
          }, 1000);
        }
        
        // 导出Excel
        function exportExcel() {
          showProgress('正在生成Excel文件...', 0);
          
          // 准备Excel数据
          const excelData = [];
          
          // 表头
          const headers = ['序号', '商品标题', '商品价格', '想要人数', '浏览量', '转化比', '状态', '上架时间', '商品链接', '主图链接'];
          if (${hasDetailData}) {
            headers.push('原价', '商品描述', '详情图片');
          }
          excelData.push(headers);
          
          // 数据行
          productData.forEach((item, index) => {
            const row = [
              index + 1,
              item.fullTitle || item.title || '',
              '¥' + (item.currentPrice || item.price || ''),
              item.wantCount || '0',
              item.viewCount || '无数据',
              item.viewCount ? ((parseInt(item.wantCount || 0) / parseInt(item.viewCount)) * 100).toFixed(1) + '%' : '无数据',
              item.status || '在售',
              item.publishTime || '未知',
              item.link || '',
              item.image || ''
            ];
            
            if (${hasDetailData}) {
              row.push(
                item.originalPrice ? '¥' + item.originalPrice : '',
                item.description || '',
                item.images ? item.images.join('\\n') : ''
              );
            }
            
            excelData.push(row);
          });
          
          showProgress('正在生成Excel文件...', 50);
          
          // 创建工作簿
          const wb = XLSX.utils.book_new();
          const ws = XLSX.utils.aoa_to_sheet(excelData);
          
          // 设置列宽
          const colWidths = [
            {wch: 8},   // 序号
            {wch: 50},  // 标题
            {wch: 12},  // 价格
            {wch: 12},  // 想要人数
            {wch: 12},  // 浏览量
            {wch: 12},  // 转化比
            {wch: 10},  // 状态
            {wch: 20},  // 上架时间
            {wch: 30},  // 链接
            {wch: 30}   // 主图链接
          ];
          
          if (${hasDetailData}) {
            colWidths.push(
              {wch: 12},  // 原价
              {wch: 50},  // 描述
              {wch: 50}   // 详情图片
            );
          }
          
          ws['!cols'] = colWidths;
          
          // 添加工作表
          XLSX.utils.book_append_sheet(wb, ws, "商品数据");
          
          showProgress('正在下载Excel文件...', 90);
          
          // 导出文件
          const fileName = 'XY商品数据_' + new Date().toISOString().slice(0,10) + '.xlsx';
          XLSX.writeFile(wb, fileName);
          
          showProgress('Excel导出完成！', 100);
          setTimeout(() => {
            hideProgress();
            alert('Excel文件导出完成！');
          }, 1000);
        }
        
        // 导出图片+Excel组合包
        async function exportCombined() {
          showProgress('正在准备组合导出...', 0);
          
          const zip = new JSZip();
          
          // 1. 添加图片文件夹
          const imgFolder = zip.folder("商品图片");
          let successCount = 0;
          let totalImages = 0;

          // 收集所有图片URL，按商品分组
          const imagesByProduct = [];
          const usedFolderNames = new Set(); // 用于跟踪已使用的文件夹名称

          productData.forEach((item, index) => {
            const productImages = [];

            // 清理商品名称，用作文件夹名 - 优先使用完整标题
            let cleanTitle = (item.fullTitle || item.title || '商品' + (index + 1))
              .substring(0, 50)  // 增加长度限制到50字符
              .replace(/[\\/:*?"<>|]/g, '')  // 移除非法字符
              .replace(/\s+/g, ' ')  // 合并多个空格
              .trim();

            // 处理重复的文件夹名称
            let finalFolderName = cleanTitle;
            let counter = 1;
            while (usedFolderNames.has(finalFolderName)) {
              finalFolderName = cleanTitle + '_' + counter;
              counter++;
            }
            usedFolderNames.add(finalFolderName);

            // 主图
            if (item.image) {
              productImages.push({
                url: item.image,
                filename: '主图.jpg',
                type: 'main'
              });
            }

            // 详情图片 - 确保不重复添加主图
            if (item.images && Array.isArray(item.images)) {
              item.images.forEach((imgUrl, imgIndex) => {
                // 检查是否与主图重复
                if (imgUrl !== item.image) {
                  productImages.push({
                    url: imgUrl,
                    filename: '详情图_' + (imgIndex + 1) + '.jpg',
                    type: 'detail'
                  });
                }
              });
            }

            // 如果没有详情图片，但有主图，至少确保主图被包含
            if (productImages.length === 1 && productImages[0].type === 'main') {
              console.log('警告: 商品 "' + cleanTitle + '" 只有主图，没有详情图片');
            }

            if (productImages.length > 0) {
              imagesByProduct.push({
                folderName: finalFolderName,
                images: productImages,
                productIndex: index + 1
              });
            }
          });

          // 计算总图片数
          totalImages = imagesByProduct.reduce((total, product) => total + product.images.length, 0);

          // 按商品下载图片并添加到对应文件夹
          let processedImages = 0;
          for (const product of imagesByProduct) {
            // 为每个商品创建子文件夹
            const productFolder = imgFolder.folder(product.folderName);

            for (const imageInfo of product.images) {
              processedImages++;
              showProgress('正在下载图片 ' + processedImages + '/' + totalImages + '...', Math.round((processedImages / totalImages) * 60));

              const blob = await urlToBlob(imageInfo.url);
              if (blob) {
                productFolder.file(imageInfo.filename, blob);
                successCount++;
              }

              // 避免请求过快
              if (processedImages % 5 === 0) {
                await new Promise(resolve => setTimeout(resolve, 100));
              }
            }
          }
          
          showProgress('正在生成Excel文件...', 70);
          
          // 2. 生成Excel数据
          const excelData = [];
          const headers = ['序号', '商品标题', '商品价格', '想要人数', '浏览量', '转化比', '状态', '上架时间', '商品链接', '主图链接'];
          if (${hasDetailData}) {
            headers.push('原价', '商品描述', '详情图片');
          }
          excelData.push(headers);
          
          productData.forEach((item, index) => {
            const row = [
              index + 1,
              item.fullTitle || item.title || '',
              '¥' + (item.currentPrice || item.price || ''),
              item.wantCount || '0',
              item.viewCount || '无数据',
              item.viewCount ? ((parseInt(item.wantCount || 0) / parseInt(item.viewCount)) * 100).toFixed(1) + '%' : '无数据',
              item.status || '在售',
              item.publishTime || '未知',
              item.link || '',
              item.image || ''
            ];
            
            if (${hasDetailData}) {
              row.push(
                item.originalPrice ? '¥' + item.originalPrice : '',
                item.description || '',
                item.images ? item.images.join('\\n') : ''
              );
            }
            
            excelData.push(row);
          });
          
          // 创建Excel工作簿
          const wb = XLSX.utils.book_new();
          const ws = XLSX.utils.aoa_to_sheet(excelData);
          
          const colWidths = [
            {wch: 8}, {wch: 50}, {wch: 12}, {wch: 12}, {wch: 12}, {wch: 12}, {wch: 10}, {wch: 20}, {wch: 30}, {wch: 30}
          ];
          if (${hasDetailData}) {
            colWidths.push({wch: 12}, {wch: 50}, {wch: 50});
          }
          ws['!cols'] = colWidths;
          
          XLSX.utils.book_append_sheet(wb, ws, "商品数据");
          
          // 将Excel转换为blob并添加到压缩包
          const excelBuffer = XLSX.write(wb, {bookType: 'xlsx', type: 'array'});
          zip.file("商品数据.xlsx", excelBuffer);
          
          showProgress('正在生成最终压缩包...', 90);
          
          // 3. 生成最终压缩包
          const content = await zip.generateAsync({type:"blob"});
          
          // 下载
          const link = document.createElement('a');
          link.href = URL.createObjectURL(content);
          link.download = 'XY商品完整数据包_' + new Date().toISOString().slice(0,10) + '.zip';
          document.body.appendChild(link);
          link.click();
          document.body.removeChild(link);
          
          showProgress('导出完成！', 100);
          setTimeout(() => {
            hideProgress();
            alert('完整数据包导出完成！\\n包含内容：\\n- Excel数据文件\\n- 商品图片文件夹 (' + successCount + '/' + totalImages + ' 张图片)\\n- 图片已按商品分类到 ' + imagesByProduct.length + ' 个文件夹');
          }, 1000);
        }
      </script>
    </head>
    <body>
      <h1>${title}</h1>
      <div class="stats">
        共采集到 ${data.length} 条商品数据，采集时间：${timestamp}
        ${hasDetailData ? '<span style="color:#1890ff;margin-left:10px">（包含详细数据）</span>' : ''}
      </div>
      
      <!-- 导出工具栏 -->
      <div class="export-toolbar">
        <button class="export-btn export-btn-images" onclick="exportImages()">
          📁 导出图片压缩包
        </button>
        <button class="export-btn export-btn-excel" onclick="exportExcel()">
          📊 导出Excel表格
        </button>
        <button class="export-btn export-btn-combined" onclick="exportCombined()">
          📦 导出完整数据包 (图片+Excel)
        </button>
      </div>
      
      <!-- 导出进度显示 -->
      <div id="export-progress" class="export-progress" style="display: none;">
        <div class="progress-text">准备中...</div>
        <div class="progress-bar">
          <div class="progress-fill"></div>
        </div>
      </div>
      <table>
        <thead>
          <tr>
            <th>序号</th>
            <th class="image-cell">商品图片</th>
            <th class="title-cell">商品标题</th>
            <th class="price-cell">商品价格</th>
            <th class="want-cell sortable" onclick="sortTable(4)">想要人数<span class="sort-indicator"></span></th>
            <th class="view-cell sortable" onclick="sortTable(5)">浏览量<span class="sort-indicator"></span></th>
            <th class="conversion-cell sortable" onclick="sortTable(6)">转化比<span class="sort-indicator"></span></th>
            <th class="status-cell">状态</th>
            <th class="publish-time-cell">上架时间</th>
            <th class="link-cell">商品链接</th>
            <th>操作</th>
          </tr>
        </thead>
        <tbody>
  `;

  data.forEach((item, index) => {
    // 确保获取最完整的标题
    const fullTitle = item.fullTitle || item.title || '';

    // 检查标题是否包含省略号且为短标题(可能是被错误截断的)
    let displayTitle = fullTitle;
    if (displayTitle.endsWith('...') && displayTitle.length < 30) {
      // 尝试去除省略号
      displayTitle = displayTitle.replace(/\.{3,}$/, '');
    }

    // 计算转化比和浏览量
    let viewCount = '无数据';
    let conversionRate = '无数据';

    if (hasDetailData && item.hasDetailData) {
      const wantCountNum = parseInt(item.wantCount) || 0;
      const viewCountNum = parseInt(item.viewCount) || 0;

      viewCount = viewCountNum;

      if (viewCountNum > 0) {
        conversionRate = ((wantCountNum / viewCountNum) * 100).toFixed(1) + '%';
      } else {
        conversionRate = '0.0%';
      }
    }

    // 基本信息行
    htmlContent += `
      <tr>
        <td>${index + 1}</td>
        <td class="image-cell">
          <div class="image-container">
            <img 
              class="product-image" 
              src="${item.image || ''}" 
              alt="商品图片"
              onerror="handleImageError(this)"
            >
          </div>
        </td>
        <td class="title-cell">${displayTitle}</td>
        <td class="price-cell">¥${item.currentPrice || item.price || ''}</td>
        <td class="want-cell">${item.wantCount || '0'}</td>
        <td class="view-cell">${viewCount}</td>
        <td class="conversion-cell">${conversionRate}</td>
        <td class="status-cell ${item.status === '已售出' ? 'status-sold' : 'status-selling'}">${item.status || '在售'}</td>
        <td class="publish-time-cell">${item.publishTime || '未知'}</td>
        <td class="link-cell"><a href="${item.link || ''}" target="_blank">查看商品</a></td>
        <td>
          ${(hasDetailData && item.hasDetailData) ? `<button class="detail-toggle" onclick="toggleDetail(${index})">查看详情</button>` : '无详情'}
        </td>
      </tr>
    `;

    // 如果有详细数据，添加详情行
    if (hasDetailData && item.hasDetailData) {
      htmlContent += `
        <tr id="detail-${index}" class="detail-row hidden-detail">
          <td colspan="11" class="detail-cell">
            <div class="detail-info">
              <div class="detail-stat">浏览: ${item.viewCount || 0}</div>
              <div class="detail-stat">想要: ${item.wantCount || 0}</div>
              <div class="detail-stat">转化比: ${conversionRate}</div>
              ${item.originalPrice ? `<div class="detail-stat">原价: ¥${item.originalPrice}</div>` : ''}
            </div>
            ${item.description ? `
              <div class="detail-content">
                ${item.description.replace(/\n/g, '<br>')}
              </div>
            ` : ''}
            ${(item.images && item.images.length > 0) ? `
              <div class="detail-info">
                <h4>商品图片:</h4>
                <div class="image-gallery">
                  ${item.images.map((img, i) => `
                    <div class="gallery-item">
                      <a href="${img}" target="_blank" title="点击查看原图">
                        <img src="${img}" alt="商品图片${i + 1}" onerror="this.parentNode.remove()">
                      </a>
                    </div>
                  `).join('')}
                </div>
              </div>
            ` : ''}
          </td>
        </tr>
      `;
    }
  });

  htmlContent += `
        </tbody>
      </table>
      <div class="footer">
        购买请联系qq:597882859 备注：闲鱼采集
      </div>
    </body>
    </html>
  `;

  return htmlContent;
}

// 导出搜索页面数据
async function exportSearchData() {
  if (searchData.length === 0) {
    console.warn('⚠️ 没有可导出的数据');
    alert('没有可导出的搜索数据！请先采集数据。');
    return;
  }

  try {
    // 获取当前的搜索关键词
    const keyword = document.querySelector('.search-input--nFg9DYqY')?.value || '商品';
    const now = new Date();
    const timestamp = `${now.getFullYear()}${String(now.getMonth() + 1).padStart(2, '0')}${String(now.getDate()).padStart(2, '0')}_${String(now.getHours()).padStart(2, '0')}${String(now.getMinutes()).padStart(2, '0')}${String(now.getSeconds()).padStart(2, '0')}`;

    // 检查是否有详细数据
    const hasDetailData = searchData.some(item => item.hasDetailData);

    // 确定采集模式标题
    const modeTitle = hasDetailData ? '搜索商品采集-详细模式' : '搜索商品采集-快速模式';

    // 导出HTML - 使用统一的HTML模板
    const htmlContent = generateUnifiedHTML({
      title: `XY商品数据（${modeTitle}）`,
      data: searchData,
      hasDetailData,
      timestamp: new Date().toLocaleString(),
      filename: `XY_${keyword}_${timestamp}.html`
    });

    const blob = new Blob([htmlContent], { type: 'text/html;charset=utf-8' });
    const url = URL.createObjectURL(blob);
    const filename = `XY_${keyword}_${timestamp}.html`;

    chrome.runtime.sendMessage({
      action: 'downloadExcel',
      data: { url, filename }
    });

    updateFloatingWindow({
      log: `导出完成，共 ${searchData.length} 条数据`
    });

  } catch (error) {
    console.error('❌ 导出失败：', error);
    alert('导出过程出现错误，请重试！');
    updateFloatingWindow({
      log: '导出失败：' + error.message
    });
  }
}

// 采集店铺数据（预留函数）
function collectShopData() {
  alert('店铺数据采集功能开发中...');
}

// 导出店铺数据到HTML
function exportShopData() {
  // 从storage获取店铺数据
  chrome.storage.local.get('shopItems', function (result) {
    if (!result.shopItems || result.shopItems.length === 0) {
      alert('没有可导出的店铺数据！请先采集数据。');
      return;
    }

    // 检查是否有详细数据
    const hasDetailData = result.shopItems.some(item => item.hasDetailData);

    // 确定采集模式标题
    const modeTitle = hasDetailData ? '店铺商品采集-详细模式' : '店铺商品采集-快速模式';

    // 使用统一的HTML生成函数
    const htmlContent = generateUnifiedHTML({
      title: `XY商品数据（${modeTitle}）`,
      data: result.shopItems,
      hasDetailData,
      timestamp: new Date().toLocaleString(),
      filename: `XY商品数据_${new Date().toLocaleDateString()}.html`
    });

    // 创建Blob并下载
    const blob = new Blob([htmlContent], { type: 'text/html' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `XY商品数据_${new Date().toLocaleDateString()}.html`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  });
}

// 导出当前商品数据到HTML
function exportCurrentItemData() {
  // 从storage获取当前商品数据
  chrome.storage.local.get('currentItem', function (result) {
    if (!result.currentItem || result.currentItem.length === 0) {
      alert('没有可导出的当前商品数据！请先采集数据。');
      return;
    }

    // 当前商品数据总是详细模式
    const modeTitle = '当前商品采集-详细模式';

    // 生成时间戳
    const now = new Date();
    const timestamp = `${now.getFullYear()}${String(now.getMonth() + 1).padStart(2, '0')}${String(now.getDate()).padStart(2, '0')}_${String(now.getHours()).padStart(2, '0')}${String(now.getMinutes()).padStart(2, '0')}${String(now.getSeconds()).padStart(2, '0')}`;

    // 使用统一的HTML生成函数
    const htmlContent = generateUnifiedHTML({
      title: `XY商品数据（${modeTitle}）`,
      data: result.currentItem,
      hasDetailData: true,
      timestamp: now.toLocaleString(),
      filename: `XY当前商品数据_${timestamp}.html`
    });

    // 创建Blob并下载
    const blob = new Blob([htmlContent], { type: 'text/html' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `XY当前商品数据_${timestamp}.html`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);

    console.log('当前商品数据导出完成');
  });
}

// 创建悬浮窗
function createFloatingWindow() {
  // 如果悬浮窗已存在，不重复创建
  if (document.getElementById('xy-floating-window')) {
    console.log('🎛️ 悬浮窗已存在，跳过创建');
    return;
  }

  const floatingWindow = document.createElement('div');
  floatingWindow.id = 'xy-floating-window';
  floatingWindow.innerHTML = `
    <div class="xy-header">
      <span>XY采集器</span>
      <div class="xy-controls">
        <button id="xy-minimize">_</button>
        <button id="xy-close">×</button>
      </div>
    </div>
    <div class="xy-content">
      <div class="xy-status">
        <div class="xy-progress-bar">
          <div class="xy-progress" style="width: 0%"></div>
        </div>
        <div class="xy-progress-text">0%</div>
      </div>
      <div class="xy-stats">
        <div>当前页码：<span id="xy-current-page">0</span></div>
        <div>采集数量：<span id="xy-total-items">0</span></div>
        <div>当前页采集：<span id="xy-current-items">0</span></div>
      </div>
      <div class="xy-buttons">
        <button id="xy-start" class="xy-btn">开始采集</button>
        <button id="xy-pause" class="xy-btn" disabled>暂停</button>
        <button id="xy-stop" class="xy-btn" disabled>停止</button>
        <button id="xy-download" class="xy-btn" disabled>下载数据</button>
      </div>
      <div class="xy-log"></div>
    </div>
  `;

  // 添加样式
  const style = document.createElement('style');
  style.textContent = `
    #xy-floating-window {
      position: fixed;
      top: 20px;
      right: 20px;
      width: 320px;
      background: white;
      border-radius: 12px;
      box-shadow: 0 8px 32px rgba(0,0,0,0.12);
      z-index: 9999;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      overflow: hidden;
      border: 1px solid rgba(0,0,0,0.06);
    }
    .xy-header {
      padding: 16px 20px;
      background: linear-gradient(135deg, #1890ff 0%, #40a9ff 100%);
      color: white;
      display: flex;
      justify-content: space-between;
      align-items: center;
      cursor: move;
      font-weight: 600;
      font-size: 16px;
    }
    .xy-controls {
      display: flex;
      gap: 8px;
    }
    .xy-controls button {
      background: rgba(255,255,255,0.2);
      border: none;
      color: white;
      cursor: pointer;
      padding: 4px 8px;
      border-radius: 4px;
      font-size: 14px;
      transition: background 0.2s;
      min-width: 24px;
      height: 24px;
      display: flex;
      align-items: center;
      justify-content: center;
    }
    .xy-controls button:hover {
      background: rgba(255,255,255,0.3);
    }
    .xy-content {
      padding: 20px;
    }
    .xy-status {
      margin-bottom: 16px;
    }
    .xy-progress-bar {
      height: 8px;
      background: #f0f0f0;
      border-radius: 4px;
      overflow: hidden;
      margin-bottom: 8px;
      position: relative;
    }
    .xy-progress {
      height: 100%;
      background: linear-gradient(90deg, #1890ff 0%, #40a9ff 100%);
      transition: width 0.3s ease;
      border-radius: 4px;
    }
    .xy-progress-text {
      text-align: center;
      font-size: 12px;
      color: #666;
      font-weight: 500;
    }
    .xy-stats {
      margin: 16px 0;
      font-size: 13px;
      background: #f8f9fa;
      padding: 12px;
      border-radius: 8px;
      border: 1px solid #e8e8e8;
    }
    .xy-stats > div {
      margin: 4px 0;
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
    .xy-stats span {
      font-weight: 600;
      color: #1890ff;
    }
    .xy-buttons {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 8px;
      margin-bottom: 16px;
    }
    .xy-btn {
      padding: 10px 12px;
      border: none;
      border-radius: 6px;
      cursor: pointer;
      font-size: 13px;
      font-weight: 500;
      transition: all 0.2s ease;
      background: linear-gradient(135deg, #1890ff 0%, #40a9ff 100%);
      color: white;
      box-shadow: 0 2px 4px rgba(24,144,255,0.2);
    }
    .xy-btn:disabled {
      background: #d9d9d9;
      color: #999;
      cursor: not-allowed;
      box-shadow: none;
    }
    .xy-btn:hover:not(:disabled) {
      background: linear-gradient(135deg, #40a9ff 0%, #69c0ff 100%);
      transform: translateY(-1px);
      box-shadow: 0 4px 8px rgba(24,144,255,0.3);
    }
    .xy-log {
      height: 120px;
      overflow-y: auto;
      font-size: 12px;
      padding: 12px;
      border: 1px solid #e8e8e8;
      border-radius: 6px;
      background: #fafafa;
      line-height: 1.4;
    }
    .xy-log::-webkit-scrollbar {
      width: 6px;
    }
    .xy-log::-webkit-scrollbar-track {
      background: #f1f1f1;
      border-radius: 3px;
    }
    .xy-log::-webkit-scrollbar-thumb {
      background: #c1c1c1;
      border-radius: 3px;
    }
    .xy-log::-webkit-scrollbar-thumb:hover {
      background: #a8a8a8;
    }
    .xy-log > div {
      margin: 2px 0;
      color: #666;
    }
    #xy-floating-window.minimized .xy-content {
      display: none;
    }
    #xy-floating-window.minimized {
      width: 200px;
    }
  `;

  document.head.appendChild(style);
  document.body.appendChild(floatingWindow);

  // 添加拖拽功能
  makeDraggable(floatingWindow);

  // 添加事件监听
  setupEventListeners(floatingWindow);
}

// 使悬浮窗可拖拽
function makeDraggable(element) {
  const header = element.querySelector('.xy-header');
  let isDragging = false;
  let currentX;
  let currentY;
  let initialX;
  let initialY;

  header.addEventListener('mousedown', startDragging);

  function startDragging(e) {
    isDragging = true;
    initialX = e.clientX - element.offsetLeft;
    initialY = e.clientY - element.offsetTop;

    document.addEventListener('mousemove', drag);
    document.addEventListener('mouseup', stopDragging);
  }

  function drag(e) {
    if (isDragging) {
      e.preventDefault();
      currentX = e.clientX - initialX;
      currentY = e.clientY - initialY;
      element.style.left = `${currentX}px`;
      element.style.top = `${currentY}px`;
    }
  }

  function stopDragging() {
    isDragging = false;
    document.removeEventListener('mousemove', drag);
    document.removeEventListener('mouseup', stopDragging);
  }
}

// 设置事件监听
function setupEventListeners(floatingWindow) {
  const minimizeBtn = floatingWindow.querySelector('#xy-minimize');
  const closeBtn = floatingWindow.querySelector('#xy-close');
  const startBtn = floatingWindow.querySelector('#xy-start');
  const pauseBtn = floatingWindow.querySelector('#xy-pause');
  const stopBtn = floatingWindow.querySelector('#xy-stop');
  const downloadBtn = floatingWindow.querySelector('#xy-download');

  minimizeBtn.addEventListener('click', () => {
    floatingWindow.classList.toggle('minimized');
    // 保存最小化状态
    chrome.storage.local.set({ 'floatingWindowMinimized': floatingWindow.classList.contains('minimized') });
  });

  closeBtn.addEventListener('click', () => {
    // 关闭悬浮窗，并标记为用户主动关闭
    floatingWindow.remove();
    chrome.storage.local.set({ 'floatingWindowClosed': true });
    showNotification('悬浮窗已关闭', 'info');
  });

  startBtn.addEventListener('click', () => {
    if (isPaused) {
      // 如果当前是暂停状态，则恢复采集
      isPaused = false;
      startBtn.disabled = true;
      pauseBtn.disabled = false;
      showNotification('采集已恢复', 'info');
      updateFloatingWindow({
        log: '采集已恢复'
      });
    } else {
      // 开始新的采集
      startBtn.disabled = true;
      pauseBtn.disabled = false;
      stopBtn.disabled = false;
      // 触发开始采集
      chrome.runtime.sendMessage({ action: 'startCollection' });
    }
  });

  pauseBtn.addEventListener('click', () => {
    isPaused = true;
    pauseBtn.disabled = true;
    startBtn.disabled = false;
    showNotification('采集已暂停', 'info');
    updateFloatingWindow({
      log: '采集已暂停，点击开始按钮恢复'
    });
  });

  stopBtn.addEventListener('click', () => {
    stopBtn.disabled = true;
    startBtn.disabled = false;
    pauseBtn.disabled = true;
    // 触发停止采集
    chrome.runtime.sendMessage({ action: 'stopCollection' });
  });

  downloadBtn.addEventListener('click', () => {
    // 直接调用导出函数
    exportSearchData();
  });
}

// 更新悬浮窗状态
function updateFloatingWindow(data) {
  const floatingWindow = document.getElementById('xy-floating-window');
  if (!floatingWindow) return;

  const progress = floatingWindow.querySelector('.xy-progress');
  const progressText = floatingWindow.querySelector('.xy-progress-text');
  const currentPage = floatingWindow.querySelector('#xy-current-page');
  const totalItems = floatingWindow.querySelector('#xy-total-items');
  const currentItems = floatingWindow.querySelector('#xy-current-items');
  const log = floatingWindow.querySelector('.xy-log');

  // 只更新提供的数据
  if (data.progress !== undefined) {
    progress.style.width = `${data.progress}%`;
    progressText.textContent = `${data.progress}%`;
  }
  if (data.currentPage !== undefined) {
    currentPage.textContent = data.currentPage;
  }
  if (data.totalItems !== undefined) {
    totalItems.textContent = data.totalItems;
  }
  if (data.currentPageItems !== undefined) {
    currentItems.textContent = data.currentPageItems;
  }
  if (data.log) {
    const logEntry = document.createElement('div');
    logEntry.textContent = `${new Date().toLocaleTimeString()} - ${data.log}`;
    log.appendChild(logEntry);
    log.scrollTop = log.scrollHeight;
  }

  // 更新下载按钮状态
  const downloadBtn = floatingWindow.querySelector('#xy-download');
  if (downloadBtn) {
    downloadBtn.disabled = !(searchData && searchData.length > 0);
  }
}

// 修改店铺采集函数
async function collectShopItems(options = {}) {
  const {
    maxItems = 0,
    minWantCount = 0,
    enableDetailCollect = false,
    detailInterval = 3
  } = options;

  console.group('🛒 开始采集店铺商品数据');
  console.time('店铺采集总耗时');
  console.log(`📊 采集配置：最大数量 ${maxItems > 0 ? maxItems : '不限'}，最小想要人数 ${minWantCount}，详细采集: ${enableDetailCollect ? '开启' : '关闭'}, 间隔: ${detailInterval}秒`);

  shouldStop = false;

  // 使用统一的悬浮窗（搜索采集的悬浮窗）
  createFloatingWindow();
  updateFloatingWindow({
    progress: 0,
    currentPage: 1, // 店铺采集只有一页
    totalItems: 0,
    currentPageItems: 0,
    log: `开始采集店铺商品...${enableDetailCollect ? '(启用详细采集)' : ''}`
  });

  await waitForPageStable();
  console.log('✅ 页面稳定，开始滚动加载商品');

  let allItems = [];
  let hasMore = true;
  let lastHeight = 0;
  const maxAttempts = 30;
  let attempts = 0;

  // 先滑动到底部加载所有商品
  console.log('📜 滚动页面加载所有商品...');
  updateFloatingWindow({
    log: '正在滚动加载所有商品...'
  });

  while (hasMore && attempts < maxAttempts && !shouldStop) {
    // 检查暂停状态
    while (isPaused && !shouldStop) {
      console.log('⏸️ 店铺采集已暂停，等待恢复...');
      updateFloatingWindow({
        log: '店铺采集已暂停，点击开始按钮恢复采集'
      });
      await new Promise(resolve => setTimeout(resolve, 1000));
    }

    if (shouldStop) break;

    window.scrollTo(0, document.documentElement.scrollHeight);
    await new Promise(resolve => setTimeout(resolve, 500)); // 减少等待时间

    const currentHeight = document.documentElement.scrollHeight;
    if (currentHeight === lastHeight) {
      hasMore = false;
      console.log('📜 已到达页面底部，停止滚动');
      continue;
    }

    lastHeight = currentHeight;
    attempts++;

    if (attempts % 5 === 0) {
      console.log(`📜 滚动加载中... (第${attempts}次滚动)`);
      updateFloatingWindow({
        log: `滚动加载中... (第${attempts}次滚动)`
      });
    }

    // 每次滚动后都检查图片加载情况
    const images = document.querySelectorAll('.feeds-image--TDRC4fV1');
    images.forEach(img => {
      if (!img.src || img.src.includes('.png')) {
        // 强制触发图片加载
        const rect = img.getBoundingClientRect();
        if (rect.top < window.innerHeight) {
          img.scrollIntoView();
        }
      }
    });
  }

  // 滚回顶部
  window.scrollTo(0, 0);
  await new Promise(resolve => setTimeout(resolve, 1000));

  // 获取所有商品
  const items = document.querySelectorAll('.feeds-item-wrap--rGdH_KoF');
  console.log(`🔍 发现 ${items.length} 个商品，开始采集...`);
  updateFloatingWindow({
    log: `发现 ${items.length} 个商品，开始采集...`
  });

  // 采集商品数据
  console.log('🚀 开始采集基础数据...');
  console.time('基础数据采集耗时');

  // 计算目标采集数量
  const targetCount = maxItems > 0 ? Math.min(maxItems, items.length) : items.length;

  for (let index = 0; index < items.length && !shouldStop; index++) {
    // 检查暂停状态
    while (isPaused && !shouldStop) {
      console.log('⏸️ 店铺基础采集已暂停，等待恢复...');
      updateFloatingWindow({
        log: '店铺基础采集已暂停，点击开始按钮恢复采集'
      });
      await new Promise(resolve => setTimeout(resolve, 1000));
    }

    if (shouldStop) break;

    const item = items[index];

    if (maxItems > 0 && allItems.length >= maxItems) {
      console.log(`✅ 已达到指定采集数量: ${maxItems}`);
      updateFloatingWindow({
        log: `已达到指定采集数量: ${maxItems}`
      });
      break;
    }

    // 确保当前商品在视图中以加载图片
    item.scrollIntoView({ behavior: 'auto', block: 'center' });
    await new Promise(resolve => setTimeout(resolve, 200));

    const itemData = extractItemData(item);

    if (itemData &&
      itemData.wantCount >= minWantCount &&
      !allItems.some(i => i.link === itemData.link)) {
      // 检查图片是否为默认的png格式
      if (itemData.image.includes('.png')) {
        // 等待图片加载完成
        const imgElement = item.querySelector('.feeds-image--TDRC4fV1');
        if (imgElement) {
          await new Promise(resolve => {
            if (imgElement.complete && !imgElement.src.includes('.png')) {
              resolve();
            } else {
              imgElement.onload = resolve;
              setTimeout(resolve, 2000); // 设置超时
            }
          });
          // 更新图片地址
          itemData.image = imgElement.src
            .replace(/^\/\//, 'https://')
            .replace(/\_\d+x\d+.*$/, '')
            .replace(/\.webp$/, '');
        }
      }

      allItems.push(itemData);

      // 更新悬浮窗进度
      const progress = Math.floor((allItems.length / targetCount) * 100);
      updateFloatingWindow({
        progress: Math.min(progress, 100),
        totalItems: allItems.length,
        currentPageItems: allItems.length,
        log: `采集商品: ${itemData.title} (想要人数:${itemData.wantCount})`
      });

      if (allItems.length % 10 === 0) {
        console.log(`🔄 已采集 ${allItems.length} 个商品`);
      }
    }

    // 更新处理进度（即使商品被过滤也要更新）
    const processProgress = Math.floor(((index + 1) / items.length) * 50); // 基础采集占50%
    updateFloatingWindow({
      progress: processProgress
    });
  }
  console.timeEnd('基础数据采集耗时');
  console.log(`✅ 基础数据采集完成，共获取 ${allItems.length} 个商品`);

  // 如果启用详细采集，为所有采集到的商品获取详细数据
  if (enableDetailCollect && allItems.length > 0 && !shouldStop) {
    console.group('🔍 开始获取详细数据');
    console.time('详细数据采集总耗时');
    console.log(`⏱️ 设置采集间隔为 ${detailInterval} 秒`);

    updateFloatingWindow({
      log: `开始获取 ${allItems.length} 个商品的详细数据...`
    });

    for (let i = 0; i < allItems.length && !shouldStop; i++) {
      // 检查暂停状态
      while (isPaused && !shouldStop) {
        console.log('⏸️ 店铺详细采集已暂停，等待恢复...');
        updateFloatingWindow({
          log: '店铺详细采集已暂停，点击开始按钮恢复采集'
        });
        await new Promise(resolve => setTimeout(resolve, 1000));
      }

      if (shouldStop) break;

      const item = allItems[i];
      if (item.link) {
        try {
          console.log(`🔄 处理商品 ${i + 1}/${allItems.length}: ${item.title}`);

          // 详细采集进度：50% + (当前详细采集进度 * 50%)
          const detailProgress = 50 + Math.floor(((i + 1) / allItems.length) * 50);
          updateFloatingWindow({
            progress: detailProgress,
            log: `正在获取详细数据 (${i + 1}/${allItems.length}): ${item.title}`
          });

          const detailData = await fetchItemDetails(item.link);

          // 保存原始的想要人数（从基础采集中获取的正确值）
          const originalWantCount = item.wantCount;

          // 合并详细数据到原始数据，但保留原始想要人数
          allItems[i] = {
            ...item,
            ...detailData,
            wantCount: originalWantCount, // 保留原始的正确想要人数
            hasDetailData: true
          };

          // 每个详情页采集后等待指定的间隔
          if (i < allItems.length - 1) {
            console.log(`⏱️ 等待 ${detailInterval} 秒后继续下一个...`);
            await new Promise(resolve => setTimeout(resolve, detailInterval * 1000));
          }

        } catch (error) {
          console.warn(`⚠️ 获取商品详情失败: ${item.title}`, error);
          allItems[i].detailError = error.message;
        }
      }
    }

    console.timeEnd('详细数据采集总耗时');
    console.log('✅ 详细数据采集完成');
    console.groupEnd();

    updateFloatingWindow({
      log: `所有商品详细数据采集完成`
    });
  }

  if (shouldStop) {
    console.log('⚠️ 采集已手动停止');
    const stopMessage = `采集已手动停止！\n已采集 ${allItems.length} 个商品数据`;
    updateFloatingWindow({
      log: '采集已手动停止'
    });

    // 显示弹窗提示
    alert(stopMessage);
  } else {
    console.log('✅ 店铺商品采集全部完成');
    const completeMessage = `店铺采集完成！\n共采集 ${allItems.length} 个商品数据${enableDetailCollect ? '\n(包含详细数据)' : ''}`;
    updateFloatingWindow({
      progress: 100,
      log: '店铺商品采集完成!'
    });

    // 显示弹窗提示
    alert(completeMessage);
  }

  // 启用下载按钮
  const floatingWindow = document.getElementById('xy-floating-window');
  if (floatingWindow) {
    const downloadBtn = floatingWindow.querySelector('#xy-download');
    if (downloadBtn) {
      downloadBtn.disabled = false;

      // 移除原有的事件监听器，避免冲突
      const newDownloadBtn = downloadBtn.cloneNode(true);
      downloadBtn.parentNode.replaceChild(newDownloadBtn, downloadBtn);

      // 重新绑定店铺数据导出事件
      newDownloadBtn.addEventListener('click', () => {
        exportShopData();
      });
    }
  }

  console.timeEnd('店铺采集总耗时');
  console.groupEnd();
  return allItems;
}

// 添加店铺商品采集按钮和配置
function addShopCollectButtons() {
  const container = document.createElement('div');
  container.style.cssText = `
    position: fixed;
    top: 100px;
    right: 20px;
    background: white;
    padding: 10px;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    z-index: 9999;
  `;

  container.innerHTML = `
    <div style="margin-bottom: 10px;">
      <label>采集数量:</label>
      <select id="maxItems" style="margin-left: 5px;">
        <option value="0">采集全部</option>
        <option value="10">前10条</option>
        <option value="20">前20条</option>
        <option value="50">前50条</option>
        <option value="100">前100条</option>
      </select>
    </div>
    <div style="margin-bottom: 10px;">
      <label>最小想要数:</label>
      <input type="number" id="minWantCount" value="0" min="0" style="width: 60px;">
    </div>
    <button id="startCollect" style="
      background: #1890ff;
      color: white;
      border: none;
      padding: 5px 15px;
      border-radius: 4px;
      cursor: pointer;
    ">开始采集</button>
  `;

  document.body.appendChild(container);

  // 绑定采集按钮点击事件
  document.getElementById('startCollect').addEventListener('click', async () => {
    const maxItems = parseInt(document.getElementById('maxItems').value);
    const minWantCount = parseInt(document.getElementById('minWantCount').value);

    try {
      const items = await collectShopItems({
        maxItems,
        minWantCount,
        enableDetailCollect,
        detailInterval
      });

      // 保存采集结果
      chrome.storage.local.set({ 'shopItems': items }, () => {
        console.log('店铺商品数据已保存');
        alert(`采集完成! 共采集 ${items.length} 个商品`);
      });
    } catch (error) {
      console.error('采集失败:', error);
      alert('采集失败: ' + error.message);
    }
  });
}












