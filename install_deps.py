#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
XY商品采集器依赖安装脚本
自动安装所需的Python包
"""

import subprocess
import sys
import os

def install_package(package_name):
    """安装单个包"""
    try:
        print(f"📦 正在安装 {package_name}...")
        result = subprocess.run([
            sys.executable, "-m", "pip", "install", package_name
        ], capture_output=True, text=True, check=True)
        print(f"✅ {package_name} 安装成功")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {package_name} 安装失败: {e}")
        print(f"错误输出: {e.stderr}")
        return False

def check_package(package_name):
    """检查包是否已安装"""
    try:
        __import__(package_name)
        return True
    except ImportError:
        return False

def main():
    """主函数"""
    print("=" * 60)
    print("📦 XY商品采集器 - 依赖包安装器")
    print("=" * 60)
    
    # 必需的包
    required_packages = [
        ("PyQt5", "PyQt5"),
        ("requests", "requests"),
        ("openpyxl", "openpyxl"),
    ]
    
    # 可选的包（用于完整功能）
    optional_packages = [
        ("selenium", "selenium"),
        ("pandas", "pandas"),
        ("Pillow", "Pillow"),
    ]
    
    print("🔍 检查必需依赖包...")
    missing_required = []
    
    for display_name, package_name in required_packages:
        if check_package(package_name):
            print(f"✅ {display_name} - 已安装")
        else:
            print(f"❌ {display_name} - 未安装")
            missing_required.append((display_name, package_name))
    
    print("\n🔍 检查可选依赖包...")
    missing_optional = []
    
    for display_name, package_name in optional_packages:
        if check_package(package_name):
            print(f"✅ {display_name} - 已安装")
        else:
            print(f"⚠️ {display_name} - 未安装（可选）")
            missing_optional.append((display_name, package_name))
    
    # 安装缺失的必需包
    if missing_required:
        print(f"\n📦 安装缺失的必需包...")
        for display_name, package_name in missing_required:
            if not install_package(package_name):
                print(f"❌ 必需包 {display_name} 安装失败，程序可能无法正常运行")
                return 1
    
    # 询问是否安装可选包
    if missing_optional:
        print(f"\n❓ 发现 {len(missing_optional)} 个可选包未安装:")
        for display_name, _ in missing_optional:
            print(f"   - {display_name}")
        
        response = input("\n是否安装可选包？(y/n): ").lower().strip()
        if response in ['y', 'yes', '是']:
            print("\n📦 安装可选包...")
            for display_name, package_name in missing_optional:
                install_package(package_name)
        else:
            print("⏭️ 跳过可选包安装")
    
    print("\n" + "=" * 60)
    print("🎉 依赖检查完成！")
    
    # 最终检查
    print("\n🔍 最终检查...")
    all_good = True
    
    for display_name, package_name in required_packages:
        if check_package(package_name):
            print(f"✅ {display_name}")
        else:
            print(f"❌ {display_name}")
            all_good = False
    
    if all_good:
        print("\n✅ 所有必需依赖已安装，可以运行应用程序！")
        print("\n📝 接下来您可以运行:")
        print("   python debug_gui.py    # GUI调试模式")
        print("   python main.py         # 完整应用程序")
        print("   python test_app.py     # 功能测试")
    else:
        print("\n❌ 部分必需依赖安装失败，请手动安装")
        print("\n📝 手动安装命令:")
        for display_name, package_name in missing_required:
            if not check_package(package_name):
                print(f"   pip install {package_name}")
    
    print("=" * 60)
    return 0 if all_good else 1

if __name__ == "__main__":
    sys.exit(main())
