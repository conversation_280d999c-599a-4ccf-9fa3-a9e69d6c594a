#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试真实的API调用
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_real_search_api():
    """测试真实的搜索API"""
    print("🔍 测试真实搜索API...")
    
    try:
        from core.collector_engine import CollectorEngine
        from core.config_manager import ConfigManager
        
        # 创建采集引擎
        config_manager = ConfigManager()
        collector = CollectorEngine(config_manager)
        
        print("📡 正在发送API请求...")
        
        # 构建搜索参数
        params = collector._build_search_params("手机", 1)
        print(f"🔧 请求参数: {params['api']}")
        print(f"🔧 关键词: 手机")
        
        # 发送请求
        response = collector.session.get(
            collector.api_config['search_api'],
            params=params,
            timeout=15
        )
        
        print(f"📊 响应状态: {response.status_code}")
        print(f"📊 响应长度: {len(response.text)} 字符")
        
        if response.status_code == 200:
            # 解析响应
            data = collector._parse_response(response.text)
            print(f"📦 解析结果: {type(data)}")
            
            if data:
                print(f"📦 数据键: {list(data.keys())}")
                
                if 'data' in data and data['data']:
                    result_data = data['data']
                    print(f"📦 结果数据键: {list(result_data.keys())}")
                    
                    if 'resultList' in result_data:
                        items = result_data['resultList']
                        print(f"✅ 获得商品数量: {len(items)}")
                        
                        if items:
                            # 显示第一个商品的信息
                            first_item = items[0]
                            print(f"\n📱 第一个商品:")
                            print(f"   ID: {first_item.get('itemId', 'N/A')}")
                            print(f"   标题: {first_item.get('title', 'N/A')[:50]}...")
                            print(f"   价格: {first_item.get('price', 'N/A')}")
                            print(f"   想要: {first_item.get('wantCnt', 'N/A')}")
                            print(f"   浏览: {first_item.get('browseCnt', 'N/A')}")
                            
                            return True
                    else:
                        print("⚠️ 响应中没有 resultList")
                else:
                    print("⚠️ 响应中没有 data 字段")
            else:
                print("⚠️ 数据解析失败")
        else:
            print(f"❌ API请求失败: {response.status_code}")
            print(f"响应内容: {response.text[:500]}...")
        
        return False
        
    except Exception as e:
        print(f"❌ API测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_api_params():
    """测试API参数构建"""
    print("\n🔧 测试API参数构建...")
    
    try:
        from core.collector_engine import CollectorEngine
        from core.config_manager import ConfigManager
        
        config_manager = ConfigManager()
        collector = CollectorEngine(config_manager)
        
        # 测试搜索参数
        params = collector._build_search_params("测试", 1)
        
        print("✅ 搜索参数构建成功:")
        for key, value in params.items():
            if key == 'data':
                print(f"   {key}: {value[:100]}..." if len(str(value)) > 100 else f"   {key}: {value}")
            else:
                print(f"   {key}: {value}")
        
        return True
        
    except Exception as e:
        print(f"❌ 参数构建测试失败: {e}")
        return False

def main():
    """主函数"""
    print("=" * 60)
    print("🧪 XY商品采集器 - 真实API测试")
    print("=" * 60)
    
    # 测试参数构建
    if not test_api_params():
        print("❌ 参数构建测试失败")
        return 1
    
    # 询问是否测试真实API
    print("\n❓ 是否测试真实的API调用？这将发送网络请求到闲鱼服务器。")
    response = input("输入 y 继续，其他键跳过: ").lower().strip()
    
    if response in ['y', 'yes', '是']:
        print("\n🌐 开始测试真实API...")
        if test_real_search_api():
            print("\n🎉 API测试成功！可以获取到真实数据。")
        else:
            print("\n⚠️ API测试失败，可能需要调整参数或检查网络。")
    else:
        print("\n⏭️ 跳过真实API测试")
    
    print("\n" + "=" * 60)
    print("📝 测试完成！")
    print("\n💡 如果API测试成功，您可以：")
    print("1. 运行 'python test_optimized_gui.py' 测试完整GUI")
    print("2. 在GUI中输入关键词进行真实搜索")
    print("3. 查看采集到的商品数据")
    print("=" * 60)
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
