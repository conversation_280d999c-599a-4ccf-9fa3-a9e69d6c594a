#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
XY商品采集器GUI调试脚本
用于调试GUI功能，不需要实际的数据采集
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def create_mock_data():
    """创建模拟数据用于测试"""
    return [
        {
            'itemId': '123456789',
            'title': '苹果iPhone 15 Pro Max 256GB 深空黑色 5G手机',
            'price': 8999.00,
            'wantCount': 156,
            'browseCnt': 2341,
            'conversionRate': 6.7,
            'status': '在售',
            'publishTime': '2025-07-30 10:30:00',
            'link': 'https://www.goofish.com/item/123456789',
            'imageUrl': 'https://example.com/image1.jpg',
            'images': ['https://example.com/image1.jpg', 'https://example.com/image2.jpg']
        },
        {
            'itemId': '987654321',
            'title': '华为Mate60 Pro 512GB 雅川青 5G手机 全网通',
            'price': 6999.00,
            'wantCount': 89,
            'browseCnt': 1205,
            'conversionRate': 7.4,
            'status': '在售',
            'publishTime': '2025-07-30 09:15:00',
            'link': 'https://www.goofish.com/item/987654321',
            'imageUrl': 'https://example.com/image3.jpg',
            'images': ['https://example.com/image3.jpg']
        },
        {
            'itemId': '456789123',
            'title': '小米14 Ultra 16GB+1TB 黑色 徕卡影像旗舰手机',
            'price': 5999.00,
            'wantCount': 234,
            'browseCnt': 3456,
            'conversionRate': 6.8,
            'status': '已售出',
            'publishTime': '2025-07-29 16:45:00',
            'link': 'https://www.goofish.com/item/456789123',
            'imageUrl': 'https://example.com/image4.jpg',
            'images': ['https://example.com/image4.jpg', 'https://example.com/image5.jpg']
        }
    ]

def test_gui_with_mock_data():
    """使用模拟数据测试GUI"""
    try:
        print("🚀 启动GUI调试模式...")
        
        from PyQt5.QtWidgets import QApplication, QMessageBox
        from ui.main_window import MainWindow
        from core.auth_manager import AuthManager
        from core.config_manager import ConfigManager
        from core.data_processor import DataProcessor
        
        # 创建应用程序
        app = QApplication(sys.argv)
        app.setApplicationName("XY商品采集器 - 调试模式")
        app.setApplicationVersion("4.0-DEBUG")
        
        # 创建管理器
        auth_manager = AuthManager()
        config_manager = ConfigManager()
        
        # 创建主窗口
        main_window = MainWindow(auth_manager, config_manager)
        
        # 添加模拟数据
        mock_data = create_mock_data()
        data_processor = DataProcessor()
        cleaned_data = data_processor.clean_and_validate(mock_data)
        
        # 更新数据模型
        main_window.collected_data = cleaned_data
        main_window.data_model.update_data(cleaned_data)
        main_window.update_statistics()
        
        # 启用导出按钮
        main_window.detail_show_btn.setEnabled(True)
        main_window.export_btn.setEnabled(True)
        
        # 显示窗口
        main_window.show()
        
        # 显示调试信息
        QMessageBox.information(
            main_window, 
            "调试模式", 
            f"🔧 GUI调试模式已启动！\n\n"
            f"✅ 已加载 {len(cleaned_data)} 条模拟数据\n"
            f"✅ 授权验证已跳过\n"
            f"✅ 所有功能可正常测试\n\n"
            f"💡 提示：\n"
            f"- 可以测试详细展示和导出功能\n"
            f"- 搜索和店铺采集需要安装selenium\n"
            f"- 关闭此对话框开始使用"
        )
        
        print("✅ GUI启动成功，已加载模拟数据")
        print("📝 提示: 关闭窗口退出程序")
        
        # 运行应用程序
        return app.exec_()
        
    except Exception as e:
        print(f"❌ GUI启动失败: {e}")
        import traceback
        traceback.print_exc()
        return 1

def test_export_functions():
    """测试导出功能"""
    try:
        print("\n🔍 测试导出功能...")
        
        from core.export_manager import ExportManager
        from core.config_manager import ConfigManager
        
        config_manager = ConfigManager()
        export_manager = ExportManager(config_manager)
        
        # 创建测试数据
        test_data = create_mock_data()
        
        # 测试HTML导出
        print("📄 测试HTML导出...")
        html_file = export_manager.export_to_html(test_data, "调试测试数据")
        print(f"✅ HTML导出成功: {html_file}")
        
        # 测试Excel导出
        print("📊 测试Excel导出...")
        excel_file = export_manager.export_to_excel(test_data, "调试测试数据")
        print(f"✅ Excel导出成功: {excel_file}")
        
        print("✅ 导出功能测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 导出功能测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("=" * 60)
    print("🔧 XY商品采集器 v4.0 - GUI调试模式")
    print("=" * 60)
    
    # 检查PyQt5
    try:
        import PyQt5
        print("✅ PyQt5 已安装")
    except ImportError:
        print("❌ PyQt5 未安装，请运行: pip install PyQt5")
        return 1
    
    # 测试导出功能
    if not test_export_functions():
        print("⚠️ 导出功能测试失败，但GUI仍可启动")
    
    print("\n🚀 启动GUI调试模式...")
    
    # 启动GUI
    return test_gui_with_mock_data()

if __name__ == "__main__":
    sys.exit(main())
