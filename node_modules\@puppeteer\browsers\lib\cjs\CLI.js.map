{"version": 3, "file": "CLI.js", "sourceRoot": "", "sources": ["../../src/CLI.ts"], "names": [], "mappings": ";AAAA;;;;GAIG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEH,+CAA8D;AAC9D,wDAA0C;AAI1C,oEAKwC;AACxC,yCAAiC;AACjC,2DAA0D;AAC1D,uDAAsD;AACtD,6CAAqC;AACrC,2CAIqB;AAcrB;;GAEG;AACH,MAAa,GAAG;IACd,UAAU,CAAS;IACnB,GAAG,CAAsB;IACzB,WAAW,CAAS;IACpB,QAAQ,CAAS;IACjB,uBAAuB,CAAU;IACjC,eAAe,CAQb;IACF,cAAc,CAAsC;IAEpD,YACE,IAiBK,EACL,EAAuB;QAEvB,IAAI,CAAC,IAAI,EAAE,CAAC;YACV,IAAI,GAAG,EAAE,CAAC;QACZ,CAAC;QACD,IAAI,OAAO,IAAI,KAAK,QAAQ,EAAE,CAAC;YAC7B,IAAI,GAAG;gBACL,SAAS,EAAE,IAAI;aAChB,CAAC;QACJ,CAAC;QACD,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,SAAS,IAAI,OAAO,CAAC,GAAG,EAAE,CAAC;QAClD,IAAI,CAAC,GAAG,GAAG,EAAE,CAAC;QACd,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,UAAU,IAAI,qBAAqB,CAAC;QAC5D,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,OAAO,IAAI,2BAAc,CAAC;QAC/C,IAAI,CAAC,uBAAuB,GAAG,IAAI,CAAC,sBAAsB,IAAI,IAAI,CAAC;QACnE,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,cAAc,CAAC;QAC3C,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,aAAa,CAAC;IAC3C,CAAC;IAUD,uBAAuB,CAAI,KAAoB,EAAE,QAAiB;QAChE,OAAO,KAAK,CAAC,UAAU,CAAC,SAAS,EAAE;YACjC,WAAW,EACT,0LAA0L;YAC5L,IAAI,EAAE,QAAQ;YACd,MAAM,EAAE,CAAC,GAAG,EAAkB,EAAE;gBAC9B,OAAO;oBACL,IAAI,EAAE,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC;oBAC7B,OAAO,EAAE,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC;iBACjC,CAAC;YACJ,CAAC;YACD,YAAY,EAAE,QAAQ;SACvB,CAAC,CAAC;IACL,CAAC;IAED,wBAAwB,CAAI,KAAoB;QAC9C,OAAO,KAAK,CAAC,MAAM,CAAC,UAAU,EAAE;YAC9B,IAAI,EAAE,QAAQ;YACd,IAAI,EAAE,uDAAuD;YAC7D,OAAO,EAAE,MAAM,CAAC,MAAM,CAAC,iCAAe,CAAC;YACvC,kBAAkB,EAAE,eAAe;SACpC,CAAC,CAAC;IACL,CAAC;IAED,oBAAoB,CAAI,KAAoB,EAAE,QAAQ,GAAG,KAAK;QAC5D,IAAI,CAAC,IAAI,CAAC,uBAAuB,EAAE,CAAC;YAClC,OAAO,KAA0C,CAAC;QACpD,CAAC;QAED,OAAO,KAAK,CAAC,MAAM,CAAC,MAAM,EAAE;YAC1B,IAAI,EAAE,QAAQ;YACd,IAAI,EAAE,gQAAgQ;YACtQ,kBAAkB,EAAE,2BAA2B;YAC/C,GAAG,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAC,OAAO,EAAE,OAAO,CAAC,GAAG,EAAE,EAAC,CAAC;YAC7C,YAAY,EAAE,QAAQ;SACvB,CAAC,CAAC;IACL,CAAC;IAED,KAAK,CAAC,GAAG,CAAC,IAAc;QACtB,MAAM,EAAC,OAAO,EAAE,KAAK,EAAC,GAAG,MAAM,MAAM,CAAC,OAAO,CAAC,CAAC;QAC/C,MAAM,EAAC,OAAO,EAAC,GAAG,MAAM,MAAM,CAAC,eAAe,CAAC,CAAC;QAChD,MAAM,aAAa,GAAG,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC;QAC3C,IAAI,MAAM,GAAG,aAAa;aACvB,UAAU,CAAC,IAAI,CAAC,WAAW,CAAC;aAC5B,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QAC1B,IAAI,IAAI,CAAC,cAAc,EAAE,CAAC;YACxB,MAAM,GAAG,MAAM,CAAC,OAAO,CACrB,IAAI,CAAC,cAAc,CAAC,GAAG,EACvB,IAAI,CAAC,cAAc,CAAC,WAAW,EAC/B,KAAK,CAAC,EAAE;gBACN,OAAO,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;YAC5B,CAAC,CACF,CAAC;QACJ,CAAC;aAAM,CAAC;YACN,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;QAC/B,CAAC;QACD,MAAM,MAAM;aACT,aAAa,CAAC,CAAC,CAAC;aAChB,IAAI,EAAE;aACN,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,aAAa,CAAC,aAAa,EAAE,CAAC,CAAC;aAClD,UAAU,EAAE,CAAC;IAClB,CAAC;IAED,MAAM,CAAC,KAA0B;QAC/B,MAAM,cAAc,GAAG,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,QAAQ,CAAC;QAClE,uEAAuE;QACvE,MAAM,cAAc,GAAG,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,WAAW,CAAC;QACxE,OAAO,KAAK;aACT,OAAO,CACN,WAAW,cAAc,EAAE,EAC3B,oNAAoN,EACpN,KAAK,CAAC,EAAE;YACN,IAAI,IAAI,CAAC,eAAe,EAAE,CAAC;gBACzB,KAAK,CAAC,OAAO,CAAC,YAAY,EAAE,6BAA6B,CAAC,CAAC;YAC7D,CAAC;YACD,KAAK;iBACF,OAAO,CACN,mBAAmB,EACnB,eAAe,cAAc,yCAAyC,CACvE;iBACA,OAAO,CACN,0BAA0B,EAC1B,4DAA4D,CAC7D;iBACA,OAAO,CACN,0BAA0B,EAC1B,oFAAoF,CACrF;iBACA,OAAO,CACN,wBAAwB,EACxB,kFAAkF,CACnF;iBACA,OAAO,CACN,uBAAuB,EACvB,iFAAiF,CAClF;iBACA,OAAO,CACN,0BAA0B,EAC1B,mEAAmE,CACpE;iBACA,OAAO,CACN,uBAAuB,EACvB,oDAAoD,CACrD;iBACA,OAAO,CACN,gCAAgC,EAChC,6DAA6D,CAC9D;iBACA,OAAO,CACN,6BAA6B,EAC7B,0DAA0D,CAC3D;iBACA,OAAO,CACN,oCAAoC,EACpC,2EAA2E,CAC5E;iBACA,OAAO,CACN,kCAAkC,EAClC,2DAA2D,CAC5D;iBACA,OAAO,CACN,uCAAuC,EACvC,6FAA6F,CAC9F;iBACA,OAAO,CACN,sCAAsC,EACtC,+DAA+D,CAChE;iBACA,OAAO,CACN,6BAA6B,EAC7B,uDAAuD,CACxD;iBACA,OAAO,CACN,oBAAoB,EACpB,oEAAoE,CACrE;iBACA,OAAO,CACN,2BAA2B,EAC3B,yDAAyD,CAC1D;iBACA,OAAO,CACN,yBAAyB,EACzB,uDAAuD,CACxD;iBACA,OAAO,CACN,+BAA+B,EAC/B,6DAA6D,CAC9D;iBACA,OAAO,CACN,wBAAwB,EACxB,sDAAsD,CACvD;iBACA,OAAO,CACN,4BAA4B,EAC5B,0DAA0D,CAC3D;iBACA,OAAO,CACN,mCAAmC,EACnC,oDAAoD,CACrD;iBACA,OAAO,CACN,mCAAmC,EACnC,8DAA8D,CAC/D,CAAC;YACJ,IAAI,IAAI,CAAC,uBAAuB,EAAE,CAAC;gBACjC,KAAK,CAAC,OAAO,CACX,iDAAiD,EACjD,2CAA2C,CAC5C,CAAC;YACJ,CAAC;YAED,MAAM,qBAAqB,GAAG,IAAI,CAAC,uBAAuB,CACxD,KAAK,EACL,CAAC,OAAO,CAAC,IAAI,CAAC,eAAe,CAAC,CAC/B,CAAC;YACF,MAAM,sBAAsB,GAAG,IAAI,CAAC,wBAAwB,CAC1D,qBAAqB,CACtB,CAAC;YACF,OAAO,IAAI,CAAC,oBAAoB,CAAC,sBAAsB,EAAE,KAAK,CAAC;iBAC5D,MAAM,CAAC,UAAU,EAAE;gBAClB,IAAI,EAAE,QAAQ;gBACd,IAAI,EAAE,2BAA2B;aAClC,CAAC;iBACD,MAAM,CAAC,cAAc,EAAE;gBACtB,IAAI,EAAE,SAAS;gBACf,IAAI,EAAE,wGAAwG;gBAC9G,OAAO,EAAE,KAAK;aACf,CAAC,CAAC;QACP,CAAC,EACD,KAAK,EAAC,IAAI,EAAC,EAAE;YACX,IAAI,IAAI,CAAC,eAAe,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC;gBAC1C,yCAAyC;gBACzC,+CAA+C;gBAC/C,iCAAiC;gBACjC,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,UAAU,CACrC,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC,GAAG,CACtC,KAAK,EAAE,CAAC,OAAO,EAAE,OAAO,CAAC,EAAE,EAAE;oBAC3B,IAAI,OAAO,CAAC,YAAY,EAAE,CAAC;wBACzB,OAAO;oBACT,CAAC;oBACD,MAAM,IAAI,CAAC,QAAQ,CAAC;wBAClB,GAAG,IAAI;wBACP,OAAO,EAAE;4BACP,IAAI,EAAE,OAAkB;4BACxB,OAAO,EAAE,OAAO,CAAC,OAAO;yBACzB;qBACF,CAAC,CAAC;gBACL,CAAC,CACF,CACF,CAAC;gBAEF,KAAK,MAAM,OAAO,IAAI,MAAM,EAAE,CAAC;oBAC7B,IAAI,OAAO,CAAC,MAAM,KAAK,UAAU,EAAE,CAAC;wBAClC,MAAM,OAAO,CAAC,MAAM,CAAC;oBACvB,CAAC;gBACH,CAAC;YACH,CAAC;iBAAM,CAAC;gBACN,MAAM,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;YAC5B,CAAC;QACH,CAAC,CACF;aACA,OAAO,CACN,kBAAkB,EAClB,8BAA8B,EAC9B,KAAK,CAAC,EAAE;YACN,KAAK;iBACF,OAAO,CACN,iCAAiC,EACjC,8BAA8B,CAC/B;iBACA,OAAO,CACN,2BAA2B,EAC3B,iEAAiE,CAClE;iBACA,OAAO,CACN,4CAA4C,EAC5C,kDAAkD,CACnD;iBACA,OAAO,CACN,kCAAkC,EAClC,iFAAiF,CAClF;iBACA,OAAO,CACN,8CAA8C,EAC9C,sEAAsE,CACvE,CAAC;YAEJ,MAAM,iBAAiB,GAAG,KAAK,CAAC,mBAAmB,CAAC;gBAClD,YAAY,EAAE,IAAI;gBAClB,qDAAqD;aACtD,CAAgD,CAAC;YAClD,MAAM,qBAAqB,GAAG,IAAI,CAAC,uBAAuB,CACxD,iBAAiB,EACjB,IAAI,CACL,CAAC;YACF,MAAM,sBAAsB,GAAG,IAAI,CAAC,wBAAwB,CAC1D,qBAAqB,CACtB,CAAC;YACF,OAAO,IAAI,CAAC,oBAAoB,CAAC,sBAAsB,CAAC;iBACrD,MAAM,CAAC,UAAU,EAAE;gBAClB,IAAI,EAAE,SAAS;gBACf,IAAI,EAAE,2BAA2B;gBACjC,OAAO,EAAE,KAAK;aACf,CAAC;iBACD,MAAM,CAAC,QAAQ,EAAE;gBAChB,IAAI,EAAE,SAAS;gBACf,IAAI,EAAE,2EAA2E;gBACjF,OAAO,EAAE,KAAK;aACf,CAAC;iBACD,MAAM,CAAC,QAAQ,EAAE;gBAChB,IAAI,EAAE,SAAS;gBACf,IAAI,EAAE,kDAAkD;gBACxD,OAAO,EAAE,KAAK;aACf,CAAC,CAAC;QACP,CAAC,EACD,KAAK,EAAC,IAAI,EAAC,EAAE;YACX,MAAM,SAAS,GAAG,IAAI,CAAC,IAAI,CAAC,EAAE,MAAM,CAAC,GAAG,CAAC,EAAE;gBACzC,OAAO,OAAO,GAAG,KAAK,QAAQ,CAAC;YACjC,CAAC,CAAC,CAAC;YAEH,MAAM,cAAc,GAAG,IAAI,CAAC,MAAM;gBAChC,CAAC,CAAC,IAAA,uCAA2B,EAAC;oBAC1B,OAAO,EAAE,IAAI,CAAC,OAAO,CAAC,IAAI;oBAC1B,kEAAkE;oBAClE,OAAO,EAAE,IAAI,CAAC,OAAO,CAAC,OAA+B;oBACrD,QAAQ,EAAE,IAAI,CAAC,QAAQ;iBACxB,CAAC;gBACJ,CAAC,CAAC,IAAA,iCAAqB,EAAC;oBACpB,OAAO,EAAE,IAAI,CAAC,OAAO,CAAC,IAAI;oBAC1B,OAAO,EAAE,IAAI,CAAC,OAAO,CAAC,OAAO;oBAC7B,QAAQ,EAAE,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,UAAU;oBACtC,QAAQ,EAAE,IAAI,CAAC,QAAQ;iBACxB,CAAC,CAAC;YACP,IAAA,kBAAM,EAAC;gBACL,IAAI,EAAE,SAAS;gBACf,cAAc;gBACd,MAAM,EAAE,IAAI,CAAC,MAAM;gBACnB,QAAQ,EAAE,IAAI,CAAC,QAAQ;aACxB,CAAC,CAAC;QACL,CAAC,CACF;aACA,OAAO,CACN,OAAO,EACP,IAAI,CAAC,uBAAuB;YAC1B,CAAC,CAAC,mEAAmE;YACrE,CAAC,CAAC,uCAAuC,IAAI,CAAC,UAAU,EAAE,EAC5D,KAAK,CAAC,EAAE;YACN,OAAO,IAAI,CAAC,oBAAoB,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;QAChD,CAAC,EACD,KAAK,EAAC,IAAI,EAAC,EAAE;YACX,MAAM,QAAQ,GAAG,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,UAAU,CAAC;YAC9C,MAAM,EAAE,GAAG,IAAI,CAAC,GAAG,IAAI,QAAQ,CAAC,eAAe,CAAC,EAAC,KAAK,EAAL,oBAAK,EAAE,MAAM,EAAN,qBAAM,EAAC,CAAC,CAAC;YACjE,EAAE,CAAC,QAAQ,CACT,oEAAoE,QAAQ,aAAa,EACzF,MAAM,CAAC,EAAE;gBACP,EAAE,CAAC,KAAK,EAAE,CAAC;gBACX,IAAI,CAAC,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC,QAAQ,CAAC,MAAM,CAAC,WAAW,EAAE,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC;oBACxD,OAAO,CAAC,GAAG,CAAC,YAAY,CAAC,CAAC;oBAC1B,OAAO;gBACT,CAAC;gBACD,MAAM,KAAK,GAAG,IAAI,gBAAK,CAAC,QAAQ,CAAC,CAAC;gBAClC,KAAK,CAAC,KAAK,EAAE,CAAC;gBACd,OAAO,CAAC,GAAG,CAAC,GAAG,QAAQ,WAAW,CAAC,CAAC;YACtC,CAAC,CACF,CAAC;QACJ,CAAC,CACF;aACA,OAAO,CACN,MAAM,EACN,oDAAoD,EACpD,KAAK,CAAC,EAAE;YACN,KAAK,CAAC,OAAO,CACX,SAAS,EACT,oDAAoD,CACrD,CAAC;YACF,IAAI,IAAI,CAAC,uBAAuB,EAAE,CAAC;gBACjC,KAAK,CAAC,OAAO,CACX,sCAAsC,EACtC,0DAA0D,CAC3D,CAAC;YACJ,CAAC;YAED,OAAO,IAAI,CAAC,oBAAoB,CAAC,KAAK,CAAC,CAAC;QAC1C,CAAC,EACD,KAAK,EAAC,IAAI,EAAC,EAAE;YACX,MAAM,QAAQ,GAAG,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,UAAU,CAAC;YAC9C,MAAM,KAAK,GAAG,IAAI,gBAAK,CAAC,QAAQ,CAAC,CAAC;YAClC,MAAM,QAAQ,GAAG,KAAK,CAAC,oBAAoB,EAAE,CAAC;YAE9C,KAAK,MAAM,OAAO,IAAI,QAAQ,EAAE,CAAC;gBAC/B,OAAO,CAAC,GAAG,CACT,GAAG,OAAO,CAAC,OAAO,IAAI,OAAO,CAAC,OAAO,KAAK,OAAO,CAAC,QAAQ,KAAK,OAAO,CAAC,cAAc,EAAE,CACxF,CAAC;YACJ,CAAC;QACH,CAAC,CACF;aACA,aAAa,CAAC,CAAC,CAAC;aAChB,IAAI,EAAE,CAAC;IACZ,CAAC;IAED,aAAa,CAAC,OAAe;QAC3B,OAAO,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,KAAK,EAAa,CAAC;IAC/C,CAAC;IAED,aAAa,CAAC,OAAe;QAC3B,MAAM,KAAK,GAAG,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;QACjC,OAAO,KAAK,CAAC,MAAM,KAAK,CAAC;YACvB,CAAC,CAAC,KAAK,CAAC,CAAC,CAAE;YACX,CAAC,CAAC,IAAI,CAAC,eAAe;gBACpB,CAAC,CAAC,QAAQ;gBACV,CAAC,CAAC,QAAQ,CAAC;IACjB,CAAC;IAED,KAAK,CAAC,QAAQ,CAAC,IAAiB;QAC9B,IAAI,CAAC,QAAQ,KAAK,IAAA,yCAAqB,GAAE,CAAC;QAC1C,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC;YAClB,MAAM,IAAI,KAAK,CAAC,yBAAyB,CAAC,CAAC;QAC7C,CAAC;QACD,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC;YACnB,MAAM,IAAI,KAAK,CAAC,wCAAwC,CAAC,CAAC;QAC5D,CAAC;QACD,IAAI,IAAI,CAAC,OAAO,CAAC,OAAO,KAAK,QAAQ,EAAE,CAAC;YACtC,MAAM,OAAO,GAAG,IAAI,CAAC,eAAe,EAAE,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;YAC1D,IAAI,CAAC,OAAO,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE,CAAC;gBACjC,MAAM,IAAI,KAAK,CAAC,+BAA+B,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC,CAAC;YACtE,CAAC;YACD,IAAI,CAAC,OAAO,CAAC,OAAO,GAAG,OAAO,CAAC,OAAO,CAAC;QACzC,CAAC;QACD,MAAM,eAAe,GAAG,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC;QAC7C,IAAI,CAAC,OAAO,CAAC,OAAO,GAAG,MAAM,IAAA,gCAAc,EACzC,IAAI,CAAC,OAAO,CAAC,IAAI,EACjB,IAAI,CAAC,QAAQ,EACb,IAAI,CAAC,OAAO,CAAC,OAAO,CACrB,CAAC;QACF,MAAM,IAAA,oBAAO,EAAC;YACZ,OAAO,EAAE,IAAI,CAAC,OAAO,CAAC,IAAI;YAC1B,OAAO,EAAE,IAAI,CAAC,OAAO,CAAC,OAAO;YAC7B,QAAQ,EAAE,IAAI,CAAC,QAAQ;YACvB,QAAQ,EAAE,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,UAAU;YACtC,wBAAwB,EAAE,SAAS;YACnC,OAAO,EAAE,IAAI,CAAC,OAAO;YACrB,YAAY,EACV,eAAe,KAAK,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,SAAS;YACxE,WAAW,EAAE,IAAI,CAAC,WAAW;SAC9B,CAAC,CAAC;QACH,OAAO,CAAC,GAAG,CACT,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,IAAI,IAAI,CAAC,OAAO,CAAC,OAAO,IAAI,IAAA,iCAAqB,EAAC;YACpE,OAAO,EAAE,IAAI,CAAC,OAAO,CAAC,IAAI;YAC1B,OAAO,EAAE,IAAI,CAAC,OAAO,CAAC,OAAO;YAC7B,QAAQ,EAAE,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,UAAU;YACtC,QAAQ,EAAE,IAAI,CAAC,QAAQ;SACxB,CAAC,EAAE,CACL,CAAC;IACJ,CAAC;CACF;AA7dD,kBA6dC"}