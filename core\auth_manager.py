#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
授权管理模块
负责用户授权验证、机器码生成、卡密管理等功能
"""

import hashlib
import platform
import uuid
import requests
import json
import time
from pathlib import Path
from typing import Dict, Any, Optional, Tuple
from utils.logger import get_logger


class AuthManager:
    """授权管理器"""
    
    def __init__(self):
        """初始化授权管理器"""
        self.logger = get_logger(self.__class__.__name__)
        
        # 授权配置（复用auth.js的配置）
        self.auth_config = {
            "API_URL": "http://api.1wxyun.com/?type=17",  # 卡密登录api
            "API_DQ": "http://api2.1wxyun.com/?type=24",  # 到期时间api
            "SOFTWARE_KEY": "2B1U4Y9F3D8Y6Z5H",  # 软件标识
            "VERSION": "1.0",  # 版本号
            "VERIFY_INTERVAL": 24 * 60 * 60 * 1000  # 验证间隔：24小时（毫秒）
        }
        
        # 本地存储文件
        self.auth_file = Path("auth_data.json")
        self.machine_code = self._generate_machine_code()
        
        # 授权状态 - 调试模式默认授权通过
        self._is_authorized = True  # 调试模式：默认授权
        self._auth_data = {}

        self.load_auth_data()
        self.logger.info("🔧 调试模式：授权验证已跳过")
    
    def _generate_machine_code(self) -> str:
        """
        生成机器码
        复用auth.js的机器码生成逻辑
        
        Returns:
            str: 机器码
        """
        try:
            # 获取系统信息
            system_info = {
                "platform": platform.system(),
                "machine": platform.machine(),
                "processor": platform.processor(),
                "node": platform.node(),
                "mac": self._get_mac_address()
            }
            
            # 生成唯一标识
            info_string = json.dumps(system_info, sort_keys=True)
            machine_code = hashlib.md5(info_string.encode()).hexdigest().upper()
            
            self.logger.info(f"机器码生成成功: {machine_code}")
            return machine_code
            
        except Exception as e:
            self.logger.error(f"机器码生成失败: {e}")
            # 使用备用方案
            return hashlib.md5(str(uuid.getnode()).encode()).hexdigest().upper()
    
    def _get_mac_address(self) -> str:
        """获取MAC地址"""
        try:
            mac = uuid.getnode()
            return ':'.join(['{:02x}'.format((mac >> elements) & 0xff) 
                           for elements in range(0, 2*6, 2)][::-1])
        except:
            return "unknown"
    
    def load_auth_data(self) -> None:
        """加载本地授权数据"""
        try:
            if self.auth_file.exists():
                with open(self.auth_file, 'r', encoding='utf-8') as f:
                    self._auth_data = json.load(f)
                self.logger.info("授权数据加载成功")
            else:
                self._auth_data = {}
                self.logger.info("未找到本地授权数据")
        except Exception as e:
            self.logger.error(f"加载授权数据失败: {e}")
            self._auth_data = {}
    
    def save_auth_data(self) -> bool:
        """保存授权数据到本地"""
        try:
            with open(self.auth_file, 'w', encoding='utf-8') as f:
                json.dump(self._auth_data, f, ensure_ascii=False, indent=2)
            self.logger.info("授权数据保存成功")
            return True
        except Exception as e:
            self.logger.error(f"保存授权数据失败: {e}")
            return False
    
    def verify_license(self, card: str = None, force_verify: bool = False) -> Dict[str, Any]:
        """
        验证授权卡密
        
        Args:
            card: 授权卡密
            force_verify: 是否强制验证
        
        Returns:
            Dict: 验证结果
        """
        try:
            # 检查是否需要重新验证
            if not force_verify and self._check_verify_interval():
                self.logger.info("距离上次验证不足24小时，跳过网络验证")
                self._is_authorized = True
                return {"success": True, "message": "验证有效期内，无需重新验证"}
            
            # 获取卡密
            if not card:
                card = self._auth_data.get("card", "")
            
            if not card:
                return {"success": False, "error": "未提供卡密，请输入有效的卡密"}
            
            # 网络验证
            result = self._verify_online(card)
            
            if result["success"]:
                # 保存验证信息
                self._auth_data.update({
                    "card": card,
                    "last_verify": int(time.time() * 1000),
                    "machine_code": self.machine_code,
                    "verify_result": result
                })
                self.save_auth_data()
                self._is_authorized = True
                self.logger.info("授权验证成功")
            
            return result
            
        except Exception as e:
            self.logger.error(f"授权验证失败: {e}")
            return {"success": False, "error": f"验证过程出错: {str(e)}"}
    
    def _verify_online(self, card: str) -> Dict[str, Any]:
        """
        在线验证卡密
        
        Args:
            card: 授权卡密
        
        Returns:
            Dict: 验证结果
        """
        try:
            # 构造请求数据
            post_data = {
                "Softid": self.auth_config["SOFTWARE_KEY"],
                "Card": card,
                "Version": self.auth_config["VERSION"],
                "Mac": self.machine_code
            }
            
            self.logger.info(f"开始网络验证: {card}")
            
            # 发送验证请求
            response = requests.post(
                self.auth_config["API_URL"],
                data=post_data,
                timeout=15,
                headers={
                    'Content-Type': 'application/x-www-form-urlencoded',
                    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
                }
            )
            
            if response.status_code != 200:
                return {"success": False, "error": f"网络请求失败: {response.status_code}"}
            
            result_text = response.text.strip()
            self.logger.info(f"验证响应: {result_text}")
            
            # 解析验证结果
            if result_text == "1":
                return {"success": True, "message": "授权验证成功"}
            elif result_text == "2":
                return {"success": False, "error": "卡密已过期"}
            elif result_text == "3":
                return {"success": False, "error": "机器码不匹配"}
            elif result_text == "4":
                return {"success": False, "error": "卡密不存在"}
            else:
                return {"success": False, "error": f"未知验证结果: {result_text}"}
                
        except requests.RequestException as e:
            self.logger.error(f"网络验证请求失败: {e}")
            return {"success": False, "error": f"网络连接失败: {str(e)}"}
        except Exception as e:
            self.logger.error(f"网络验证异常: {e}")
            return {"success": False, "error": f"验证异常: {str(e)}"}
    
    def _check_verify_interval(self) -> bool:
        """检查验证间隔"""
        last_verify = self._auth_data.get("last_verify", 0)
        current_time = int(time.time() * 1000)
        return (current_time - last_verify) < self.auth_config["VERIFY_INTERVAL"]
    
    def check_auth(self) -> bool:
        """
        检查授权状态

        Returns:
            bool: 是否已授权
        """
        # 调试模式：直接返回True
        self.logger.info("🔧 调试模式：授权检查跳过")
        return True
    
    def get_machine_code(self) -> str:
        """获取机器码"""
        return self.machine_code
    
    def logout(self) -> None:
        """注销授权"""
        self._is_authorized = False
        self._auth_data = {}
        if self.auth_file.exists():
            self.auth_file.unlink()
        self.logger.info("授权已注销")
    
    @property
    def is_authorized(self) -> bool:
        """是否已授权"""
        return self._is_authorized
