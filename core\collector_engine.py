#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据采集引擎模块
基于Selenium实现闲鱼商品数据采集
"""

import time
import json
import re
from typing import List, Dict, Any, Optional, Callable
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from selenium.common.exceptions import TimeoutException, WebDriverException
from utils.logger import get_logger


class CollectorEngine:
    """数据采集引擎"""
    
    def __init__(self, config_manager):
        """
        初始化采集引擎
        
        Args:
            config_manager: 配置管理器
        """
        self.config_manager = config_manager
        self.logger = get_logger(self.__class__.__name__)
        self.driver = None
        self.is_collecting = False
        self.should_stop = False
        
        # 注入的JavaScript代码（从content.js提取核心逻辑）
        self.js_extractor = self._get_js_extractor_code()
    
    def _get_js_extractor_code(self) -> str:
        """获取JavaScript数据提取代码"""
        return """
        // 数据提取器
        window.XYDataExtractor = {
            apiDataCache: new Map(),
            
            // 设置API数据拦截器
            setupInterceptor: function() {
                console.log('🔍 设置API数据拦截器...');
                
                // 保存原始fetch
                const originalFetch = window.fetch;
                
                // 重写fetch函数
                window.fetch = async function(...args) {
                    const response = await originalFetch.apply(this, args);
                    const url = args[0];
                    
                    if (typeof url === 'string') {
                        // 搜索商品API
                        if (url.includes('mtop.taobao.idlehome.home.webpc.feed')) {
                            console.log('🎯 拦截到搜索API请求:', url);
                            try {
                                const clonedResponse = response.clone();
                                const data = await clonedResponse.json();
                                window.XYDataExtractor.processSearchApiData(url, data);
                            } catch (error) {
                                console.warn('⚠️ 解析搜索API数据失败:', error);
                            }
                        }
                        // 店铺商品API
                        else if (url.includes('mtop.idle.web.xyh.item.list')) {
                            console.log('🎯 拦截到店铺API请求:', url);
                            try {
                                const clonedResponse = response.clone();
                                const data = await clonedResponse.json();
                                window.XYDataExtractor.processShopApiData(url, data);
                            } catch (error) {
                                console.warn('⚠️ 解析店铺API数据失败:', error);
                            }
                        }
                        // 商品详情API
                        else if (url.includes('mtop') && url.includes('detail')) {
                            console.log('🎯 拦截到详情API请求:', url);
                            try {
                                const clonedResponse = response.clone();
                                const data = await clonedResponse.json();
                                window.XYDataExtractor.processDetailApiData(url, data);
                            } catch (error) {
                                console.warn('⚠️ 解析详情API数据失败:', error);
                            }
                        }
                    }
                    return response;
                };
                
                console.log('✅ API拦截器设置完成');
            },
            
            // 处理搜索API数据
            processSearchApiData: function(url, data) {
                try {
                    console.log('📦 处理搜索API数据:', data);
                    if (data && data.data && data.data.resultList) {
                        console.log('📦 搜索结果数量:', data.data.resultList.length);
                        data.data.resultList.forEach(item => {
                            if (item.itemId) {
                                this.apiDataCache.set('search_' + item.itemId, {
                                    itemId: item.itemId,
                                    title: item.title,
                                    price: item.price,
                                    wantCnt: item.wantCnt,
                                    browseCnt: item.browseCnt,
                                    proPolishTime: item.proPolishTime,
                                    imageInfos: item.imageInfos,
                                    status: item.status || '在售'
                                });
                            }
                        });
                    }
                } catch (error) {
                    console.warn('⚠️ 处理搜索API数据失败:', error);
                }
            },

            // 处理店铺API数据
            processShopApiData: function(url, data) {
                try {
                    console.log('📦 处理店铺API数据:', data);
                    if (data && data.data && data.data.itemList) {
                        console.log('📦 店铺商品数量:', data.data.itemList.length);
                        data.data.itemList.forEach(item => {
                            if (item.itemId) {
                                this.apiDataCache.set('shop_' + item.itemId, {
                                    itemId: item.itemId,
                                    title: item.title,
                                    price: item.price,
                                    wantCnt: item.wantCnt,
                                    browseCnt: item.browseCnt,
                                    proPolishTime: item.proPolishTime,
                                    imageInfos: item.imageInfos,
                                    status: item.status || '在售'
                                });
                            }
                        });
                    }
                } catch (error) {
                    console.warn('⚠️ 处理店铺API数据失败:', error);
                }
            },

            // 处理详情API数据
            processDetailApiData: function(url, data) {
                try {
                    console.log('📦 处理详情API数据:', data);
                    if (data && data.data && data.data.itemDO) {
                        const item = data.data.itemDO;
                        console.log('📦 处理详情页数据:', item.itemId);
                        if (item.itemId) {
                            this.apiDataCache.set('detail_' + item.itemId, {
                                itemId: item.itemId,
                                title: item.title,
                                price: item.price,
                                wantCnt: item.wantCnt,
                                browseCnt: item.browseCnt,
                                favCnt: item.favCnt,
                                desc: item.desc,
                                imageInfos: item.imageInfos,
                                proPolishTime: item.proPolishTime
                            });
                        }
                    }
                } catch (error) {
                    console.warn('⚠️ 处理详情API数据失败:', error);
                }
            },
            
            // 从页面DOM提取商品数据
            extractFromDOM: function() {
                const items = [];
                const itemElements = document.querySelectorAll('a[href*="/item/"]');
                
                itemElements.forEach((element, index) => {
                    try {
                        // 提取基础信息
                        const titleElement = element.querySelector('.main-title--sMrtWSJa');
                        const priceElement = element.querySelector('.price--pzTeu3Ll');
                        const wantElement = element.querySelector('.want--ecByv3Sr');
                        const imageElement = element.querySelector('img');
                        
                        const title = titleElement ? titleElement.textContent.trim() : '';
                        const priceText = priceElement ? priceElement.textContent.trim() : '';
                        const wantText = wantElement ? wantElement.textContent.trim() : '';
                        const imageUrl = imageElement ? imageElement.src : '';
                        const link = element.href;
                        
                        // 提取商品ID
                        let itemId = null;
                        const idMatch = link.match(/item\\/([^/?]+)/);
                        if (idMatch) {
                            itemId = idMatch[1];
                        }
                        
                        // 解析价格
                        const priceMatch = priceText.match(/([\\d.]+)/);
                        const price = priceMatch ? parseFloat(priceMatch[1]) : 0;
                        
                        // 解析想要人数
                        const wantMatch = wantText.match(/(\\d+)/);
                        const wantCount = wantMatch ? parseInt(wantMatch[1]) : 0;
                        
                        // 检查是否已售出
                        const isSold = element.querySelector('.itemStatus--FGs2IDvi') !== null;
                        
                        if (title && itemId) {
                            const item = {
                                index: index + 1,
                                itemId: itemId,
                                title: title,
                                price: price,
                                wantCount: wantCount,
                                browseCnt: 0,
                                status: isSold ? '已售出' : '在售',
                                link: link,
                                imageUrl: imageUrl,
                                timestamp: new Date().toLocaleString()
                            };
                            
                            // 尝试从API缓存获取更详细的数据
                            const apiData = this.apiDataCache.get('search_' + itemId);
                            if (apiData) {
                                Object.assign(item, apiData);
                            }
                            
                            items.push(item);
                        }
                    } catch (error) {
                        console.error('提取商品数据失败:', error);
                    }
                });
                
                return items;
            },
            
            // 获取所有缓存的API数据
            getAllApiData: function() {
                const result = {};
                this.apiDataCache.forEach((value, key) => {
                    result[key] = value;
                });
                return result;
            },
            
            // 清空缓存
            clearCache: function() {
                this.apiDataCache.clear();
            }
        };
        
        // 自动设置拦截器
        window.XYDataExtractor.setupInterceptor();
        
        return 'XY数据提取器已注入';
        """
    
    def init_driver(self, headless: bool = True) -> bool:
        """
        初始化WebDriver
        
        Args:
            headless: 是否使用无头模式
        
        Returns:
            bool: 初始化是否成功
        """
        try:
            chrome_options = Options()
            
            if headless:
                chrome_options.add_argument('--headless')
            
            # 基础配置
            chrome_options.add_argument('--no-sandbox')
            chrome_options.add_argument('--disable-dev-shm-usage')
            chrome_options.add_argument('--disable-gpu')
            chrome_options.add_argument('--window-size=1920,1080')
            
            # 用户代理
            chrome_options.add_argument(
                '--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 '
                '(KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
            )
            
            # 禁用图片加载以提高速度
            prefs = {
                "profile.managed_default_content_settings.images": 2,
                "profile.default_content_setting_values.notifications": 2
            }
            chrome_options.add_experimental_option("prefs", prefs)
            
            # 创建WebDriver
            self.driver = webdriver.Chrome(options=chrome_options)
            self.driver.set_page_load_timeout(30)
            self.driver.implicitly_wait(10)
            
            self.logger.info("WebDriver初始化成功")
            return True
            
        except Exception as e:
            self.logger.error(f"WebDriver初始化失败: {e}")
            return False
    
    def inject_extractor(self) -> bool:
        """
        注入数据提取器
        
        Returns:
            bool: 注入是否成功
        """
        try:
            result = self.driver.execute_script(self.js_extractor)
            self.logger.info(f"数据提取器注入成功: {result}")
            return True
        except Exception as e:
            self.logger.error(f"数据提取器注入失败: {e}")
            return False
    
    def wait_for_page_load(self, timeout: int = 10) -> bool:
        """
        等待页面加载完成
        
        Args:
            timeout: 超时时间
        
        Returns:
            bool: 是否加载成功
        """
        try:
            WebDriverWait(self.driver, timeout).until(
                lambda driver: driver.execute_script("return document.readyState") == "complete"
            )
            # 额外等待一下，确保动态内容加载
            time.sleep(2)
            return True
        except TimeoutException:
            self.logger.warning("页面加载超时")
            return False
    
    def collect_search_data(self, keyword: str, max_pages: int = 3, 
                          min_want_count: int = 10, 
                          progress_callback: Optional[Callable] = None) -> List[Dict[str, Any]]:
        """
        采集搜索页面数据
        
        Args:
            keyword: 搜索关键词
            max_pages: 最大页数
            min_want_count: 最小想要人数
            progress_callback: 进度回调函数
        
        Returns:
            List[Dict]: 采集到的商品数据
        """
        if not self.driver:
            if not self.init_driver():
                return []
        
        self.is_collecting = True
        self.should_stop = False
        collected_items = []
        
        try:
            # 构造搜索URL
            search_url = f"https://www.goofish.com/search?q={keyword}"
            self.logger.info(f"开始采集搜索数据: {keyword}")
            
            for page in range(max_pages):
                if self.should_stop:
                    break
                
                # 访问页面
                page_url = f"{search_url}&page={page + 1}" if page > 0 else search_url
                self.driver.get(page_url)
                
                # 等待页面加载
                if not self.wait_for_page_load():
                    self.logger.warning(f"第{page + 1}页加载失败")
                    continue
                
                # 注入数据提取器
                self.inject_extractor()
                
                # 等待API数据
                time.sleep(3)
                
                # 提取数据
                items = self.extract_page_data()
                
                # 过滤数据
                filtered_items = [
                    item for item in items 
                    if item.get('wantCount', 0) >= min_want_count
                ]
                
                collected_items.extend(filtered_items)
                
                # 更新进度
                if progress_callback:
                    progress_callback({
                        'current_page': page + 1,
                        'total_pages': max_pages,
                        'current_items': len(filtered_items),
                        'total_items': len(collected_items),
                        'progress': int((page + 1) / max_pages * 100)
                    })
                
                self.logger.info(f"第{page + 1}页采集完成，获得{len(filtered_items)}条数据")
                
                # 页面间隔
                if page < max_pages - 1:
                    time.sleep(2)
            
            self.logger.info(f"搜索采集完成，共获得{len(collected_items)}条数据")
            return collected_items
            
        except Exception as e:
            self.logger.error(f"搜索采集失败: {e}")
            return collected_items
        finally:
            self.is_collecting = False
    
    def extract_page_data(self) -> List[Dict[str, Any]]:
        """
        从当前页面提取数据
        
        Returns:
            List[Dict]: 提取到的商品数据
        """
        try:
            # 执行数据提取
            items = self.driver.execute_script("return window.XYDataExtractor.extractFromDOM();")
            
            # 获取API缓存数据
            api_data = self.driver.execute_script("return window.XYDataExtractor.getAllApiData();")
            
            # 合并数据
            for item in items:
                item_id = item.get('itemId')
                if item_id:
                    # 尝试从不同的API缓存中获取数据
                    api_item = None
                    for prefix in ['search_', 'shop_', 'detail_']:
                        cache_key = f'{prefix}{item_id}'
                        if cache_key in api_data:
                            api_item = api_data[cache_key]
                            break

                    if api_item:
                        item.update(api_item)

                    # 计算转化比
                    want_count = item.get('wantCnt', 0) or item.get('wantCount', 0)
                    browse_count = item.get('browseCnt', 0)
                    if browse_count > 0:
                        item['conversionRate'] = round(want_count / browse_count * 100, 2)
                    else:
                        item['conversionRate'] = 0
            
            return items
            
        except Exception as e:
            self.logger.error(f"数据提取失败: {e}")
            return []
    
    def collect_shop_data(self, shop_url: str, max_items: int = 0,
                         min_want_count: int = 10,
                         progress_callback: Optional[Callable] = None) -> List[Dict[str, Any]]:
        """
        采集店铺数据
        
        Args:
            shop_url: 店铺URL
            max_items: 最大采集数量，0表示不限制
            min_want_count: 最小想要人数
            progress_callback: 进度回调函数
        
        Returns:
            List[Dict]: 采集到的商品数据
        """
        if not self.driver:
            if not self.init_driver():
                return []
        
        self.is_collecting = True
        self.should_stop = False
        collected_items = []
        
        try:
            self.logger.info(f"开始采集店铺数据: {shop_url}")
            
            # 访问店铺页面
            self.driver.get(shop_url)
            
            if not self.wait_for_page_load():
                self.logger.error("店铺页面加载失败")
                return []
            
            # 注入数据提取器
            self.inject_extractor()
            
            # 等待API数据
            time.sleep(3)
            
            # 提取数据
            items = self.extract_page_data()
            
            # 过滤和限制数量
            filtered_items = [
                item for item in items 
                if item.get('wantCount', 0) >= min_want_count
            ]
            
            if max_items > 0:
                filtered_items = filtered_items[:max_items]
            
            collected_items = filtered_items
            
            # 更新进度
            if progress_callback:
                progress_callback({
                    'current_page': 1,
                    'total_pages': 1,
                    'current_items': len(collected_items),
                    'total_items': len(collected_items),
                    'progress': 100
                })
            
            self.logger.info(f"店铺采集完成，共获得{len(collected_items)}条数据")
            return collected_items
            
        except Exception as e:
            self.logger.error(f"店铺采集失败: {e}")
            return collected_items
        finally:
            self.is_collecting = False
    
    def stop_collection(self):
        """停止采集"""
        self.should_stop = True
        self.is_collecting = False
        self.logger.info("采集已停止")
    
    def close(self):
        """关闭WebDriver"""
        if self.driver:
            try:
                self.driver.quit()
                self.logger.info("WebDriver已关闭")
            except Exception as e:
                self.logger.error(f"关闭WebDriver失败: {e}")
            finally:
                self.driver = None
