// 授权验证状态
let isAuthorized = false;

// 检查授权状态
async function checkAuthorization() {
  try {
    // 加载auth.js模块
    if (!window.AuthModule) {
      await loadAuthModule();
    }

    // 尝试验证已保存的卡密
    const result = await AuthModule.verifyLicense();

    if (result.success) {
      isAuthorized = true;
      console.log('授权验证成功:', result.message);
      return true;
    } else {
      isAuthorized = false;
      console.log('授权验证失败:', result.error);
      return false;
    }
  } catch (error) {
    console.error('授权检查出错:', error);
    isAuthorized = false;
    return false;
  }
}

// 动态加载auth模块
function loadAuthModule() {
  return new Promise((resolve, reject) => {
    const script = document.createElement('script');
    script.src = 'auth.js';
    script.onload = resolve;
    script.onerror = reject;
    document.head.appendChild(script);
  });
}

// 显示授权窗口
function showAuthWindow() {
  chrome.tabs.create({
    url: chrome.runtime.getURL('auth.html'),
    active: true
  });
  window.close();
}

// 检查授权并执行操作
async function checkAuthAndExecute(callback) {
  if (!isAuthorized) {
    const authorized = await checkAuthorization();
    if (!authorized) {
      showAuthWindow();
      return;
    }
  }

  // 授权通过，执行回调
  callback();
}

// 快速检查授权状态（不进行网络验证）
async function quickAuthCheck() {
  try {
    // 加载auth.js模块
    if (!window.AuthModule) {
      await loadAuthModule();
    }

    // 检查是否有保存的卡密
    const savedCard = await AuthModule.loadCard();
    if (!savedCard) {
      console.log('没有保存的卡密');
      return false;
    }

    // 检查是否需要重新验证
    const needVerify = await AuthModule.needReVerify();
    if (!needVerify) {
      console.log('验证有效期内，授权有效');
      isAuthorized = true;
      return true;
    }

    console.log('需要重新验证');
    return false;
  } catch (error) {
    console.error('快速授权检查出错:', error);
    return false;
  }
}

// 处理折叠面板
function setupCollapsible(headerId, contentId) {
  const header = document.getElementById(headerId);
  const content = document.getElementById(contentId);
  const arrow = header.querySelector('.arrow');

  header.addEventListener('click', () => {
    const isActive = content.classList.contains('active');

    // 切换内容区域
    content.classList.toggle('active');
    arrow.classList.toggle('active');
    header.classList.toggle('active');

    // 添加动画效果
    if (!isActive) {
      // 展开时的动画
      content.style.maxHeight = content.scrollHeight + 'px';
    } else {
      // 收起时的动画
      content.style.maxHeight = '0px';
    }
  });
}

// 初始化页面
document.addEventListener('DOMContentLoaded', function () {
  // 设置初始状态：搜索区域展开，店铺区域折叠
  const searchContent = document.getElementById('searchContent');
  const searchHeader = document.getElementById('searchHeader');
  const searchArrow = searchHeader.querySelector('.arrow');

  // 初始状态：搜索区域展开，店铺区域折叠
  searchContent.classList.add('active');
  searchArrow.classList.add('active');
  searchHeader.classList.add('active');
  searchContent.style.maxHeight = searchContent.scrollHeight + 'px';

  // 设置折叠面板事件监听器
  setupCollapsible('searchHeader', 'searchContent');
  setupCollapsible('shopHeader', 'shopContent');
  setupCollapsible('currentItemHeader', 'currentItemContent');

  // 搜索页面采集按钮
  document.getElementById('collectSearch').addEventListener('click', function () {
    checkAuthAndExecute(() => {
      sendMessageToActiveTab({
        action: 'collectSearch',
        minWantCount: parseInt(document.getElementById('minWantCount').value) || 0,
        pageCount: parseInt(document.getElementById('pageCount').value) || 1,
        enableDetailCollect: document.getElementById('detailCollectSearch').checked,
        detailInterval: parseInt(document.getElementById('detailIntervalSearch').value) || 3
      });
    });
  });

  // 停止采集按钮
  document.getElementById('stopCollect').addEventListener('click', function () {
    sendMessageToActiveTab({
      action: 'stopCollection'
    });
  });

  // 搜索数据导出按钮
  document.getElementById('exportSearch').addEventListener('click', function () {
    checkAuthAndExecute(() => {
      sendMessageToActiveTab({
        action: 'exportSearch'
      });
    });
  });

  // 店铺采集按钮
  document.getElementById('collectShop').addEventListener('click', function () {
    checkAuthAndExecute(() => {
      sendMessageToActiveTab({
        action: 'collectShop',
        maxItems: parseInt(document.getElementById('shopMaxItems').value) || 0,
        minWantCount: parseInt(document.getElementById('shopMinWantCount').value) || 0,
        enableDetailCollect: document.getElementById('detailCollectShop').checked,
        detailInterval: parseInt(document.getElementById('detailIntervalShop').value) || 3
      });
    });
  });

  // 店铺数据导出按钮
  document.getElementById('exportShop').addEventListener('click', function () {
    checkAuthAndExecute(() => {
      sendMessageToActiveTab({
        action: 'exportShop'
      });
    });
  });

  // 当前商品采集按钮
  document.getElementById('collectCurrentItem').addEventListener('click', function () {
    console.log('📱 点击了采集当前商品按钮');
    checkAuthAndExecute(() => {
      console.log('📱 授权验证通过，发送采集消息');
      sendMessageToActiveTab({
        action: 'collectCurrentItem'
      });
    });
  });

  // 当前商品数据导出按钮
  document.getElementById('exportCurrentItem').addEventListener('click', function () {
    console.log('📊 点击了导出当前商品数据按钮');
    checkAuthAndExecute(() => {
      sendMessageToActiveTab({
        action: 'exportCurrentItem'
      });
    });
  });

  // 加载存储的配置
  loadSettings();

  // 初始授权检查 - 使用快速检查，避免频繁网络验证
  quickAuthCheck().then(authorized => {
    if (authorized) {
      console.log('授权有效，无需重新验证');
    } else {
      console.log('需要授权验证');
      // 只有在快速检查失败时才进行完整验证
      checkAuthorization().then(fullAuthorized => {
        if (!fullAuthorized) {
          console.log('完整验证也失败，需要重新授权');
        }
      });
    }
  });
});

// 保存所有设置
function saveSettings() {
  const settings = {
    minWantCount: document.getElementById('minWantCount').value,
    pageCount: document.getElementById('pageCount').value,
    detailCollectSearch: document.getElementById('detailCollectSearch').checked,
    detailIntervalSearch: document.getElementById('detailIntervalSearch').value,
    shopMaxItems: document.getElementById('shopMaxItems').value,
    shopMinWantCount: document.getElementById('shopMinWantCount').value,
    detailCollectShop: document.getElementById('detailCollectShop').checked,
    detailIntervalShop: document.getElementById('detailIntervalShop').value
  };

  chrome.storage.local.set({ settings: settings }, function () {
    console.log('设置已保存');
  });
}

// 加载存储的设置
function loadSettings() {
  chrome.storage.local.get('settings', function (data) {
    if (data.settings) {
      document.getElementById('minWantCount').value = data.settings.minWantCount || 10;
      document.getElementById('pageCount').value = data.settings.pageCount || 1;
      document.getElementById('detailCollectSearch').checked = data.settings.detailCollectSearch || false;
      document.getElementById('detailIntervalSearch').value = data.settings.detailIntervalSearch || 2;
      document.getElementById('shopMaxItems').value = data.settings.shopMaxItems || 0;
      document.getElementById('shopMinWantCount').value = data.settings.shopMinWantCount || 10;
      document.getElementById('detailCollectShop').checked = data.settings.detailCollectShop || false;
      document.getElementById('detailIntervalShop').value = data.settings.detailIntervalShop || 2;
    }
  });
}

// 向当前激活标签发送消息
function sendMessageToActiveTab(message) {
  console.log('📤 准备发送消息:', message);
  chrome.tabs.query({ active: true, currentWindow: true }, function (tabs) {
    if (tabs[0]) {
      console.log('📤 找到活动标签页:', tabs[0].url);
      chrome.tabs.sendMessage(tabs[0].id, message, function (response) {
        if (chrome.runtime.lastError) {
          console.error('❌ 发送消息错误：', chrome.runtime.lastError);
          alert('发送消息失败：' + chrome.runtime.lastError.message);
        } else {
          console.log('📥 收到响应:', response);
          if (response && response.success) {
            // 保存设置
            saveSettings();
            window.close();
          } else if (response && response.error) {
            alert('操作失败：' + response.error);
          }
        }
      });
    } else {
      console.error('❌ 未找到活动标签页');
      alert('未找到活动标签页');
    }
  });
}

// 监听来自content和background的消息
chrome.runtime.onMessage.addListener(function (request, _sender, sendResponse) {
  if (request.action === 'downloadExcel') {
    // 处理Excel下载请求
    chrome.downloads.download({
      url: request.data.url,
      filename: request.data.filename,
      saveAs: true
    });

    sendResponse({ success: true });
    return true;
  }
}); 