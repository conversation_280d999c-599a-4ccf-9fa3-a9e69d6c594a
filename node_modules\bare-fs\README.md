# bare-fs

Native file system for Javascript.

```
npm i bare-fs
```

## Usage

```js
const fs = require('bare-fs')

// Currently supports:

fs.access
fs.chmod
fs.close
fs.copyFile
fs.exists
fs.fstat
fs.ftruncate
fs.lstat
fs.mkdir
fs.open
fs.opendir
fs.read
fs.readdir
fs.readlink
fs.readv
fs.realpath
fs.rename
fs.rmdir
fs.stat
fs.symlink
fs.unlink
fs.watch
fs.write
fs.writev

fs.appendFile
fs.readFile
fs.writeFile

fs.promises.access
fs.promises.chmod
fs.promises.copyFile
fs.promises.lstat
fs.promises.mkdir
fs.promises.opendir
fs.promises.readdir
fs.promises.readlink
fs.promises.realpath
fs.promises.rename
fs.promises.rmdir
fs.promises.stat
fs.promises.symlink
fs.promises.unlink
fs.promises.watch

fs.promises.appendFile
fs.promises.readFile
fs.promises.writeFile

fs.createReadStream
fs.createWriteStream

fs.accessSync
fs.chmodSync
fs.closeSync
fs.copyFileSync
fs.existsSync
fs.fchmodSync
fs.fstatSync
fs.ftruncateSync
fs.lstatSync
fs.mkdirSync
fs.openSync
fs.opendirSync
fs.readSync
fs.readdirSync
fs.readlinkSync
fs.realpathSync
fs.renameSync
fs.rmdirSync
fs.statSync
fs.symlinkSync
fs.unlinkSync
fs.writeSync

fs.appendFileSync
fs.readFileSync
fs.writeFileSync
```

## License

Apache-2.0
